<script setup>
// @ts-nocheck
import VChart from 'vue-echarts';
import * as echarts from 'echarts';
import { computed } from 'vue';
import { getHealthCondition } from '/@/api/dataview/base'
import { useRoadFilterApi } from '/@/composables/useApi';
import { dataview } from '/@/stores/dataview';
const store = dataview();
// 获取API数据
const { data: apiData, isLoading, error } = useRoadFilterApi(getHealthCondition);

// 根据API数据计算健康状态统计
const healths = computed(() => {
  if (!apiData.value) return [];

  const allData = apiData.value['全部'] || {
    基本完好: 0,
    轻微异常: 0,
    中等异常: 0,
    严重异常: 0
  };

  return [
    { label: '基本完好', value: allData.基本完好, color: 'text-sky-400' },
    { label: '轻微异常', value: allData.轻微异常, color: 'text-yellow-400' },
    { label: '中等异常', value: allData.中等异常, color: 'text-orange-400' },
    { label: '严重异常', value: allData.严重异常, color: 'text-red-500' },
  ];
});

// 结构类型
const structureTypes = ['桥梁', '隧道', '边坡'];
const handleClick = (item) => {
  const healthDegree = {
    基本完好: 0,
    轻微异常: 1,
    中等异常: 2,
    严重异常: 3
  }
  store.setFilterList([{ id: 'healthDegree', path: ['healthDegree'], value: healthDegree[item.label] }]);
};
// 创建图表配置生成函数
function createChartOption(structureType) {
  // 默认值
  const defaultHealth = {
    基本完好: 0,
    轻微异常: 0,
    中等异常: 0,
    严重异常: 0
  };

  // 获取当前结构类型的数据
  const structureData = apiData.value?.[structureType] || defaultHealth;

  // 计算健康度百分比 (基本完好占总数的比例)
  const total = Object.values(structureData).reduce((sum, val) => sum + val, 0);

  if (total === 0) {
    return {
      title: [
        {
          text: structureType,
          left: 'center',
          top: '35%',
          textStyle: {
            fontSize: 15,
            color: '#FACC15',
            fontWeight: 'bold'
          }
        },
        {
          // text: '健康度',
          left: 'center',
          top: '44%',
          textStyle: {
            fontSize: 12,
            color: '#FFFFFF',
            fontWeight: 'normal'
          }
        },
        {
          text: `—`,
          left: 'center',
          bottom: '0%',
          textStyle: {
            fontSize: 15,
            color: '#FFFFFF',
            fontWeight: 'normal'
          }
        }
      ],
      series: [
        {
          name: '健康度分布',
          type: 'pie',
          radius: ['65%', '75%'],
          center: ['50%', '42%'],
          avoidLabelOverlap: false,
          padAngle: 5,
          itemStyle: {
            borderRadius: 10
          },
          label: {
            show: false,
            position: 'center'
          },
          labelLine: {
            show: false
          },
          startAngle: 150,
          emphasis: {
            scale: false
          },
          data: [
            { value: 1, name: '基本完好', itemStyle: { color: '#0ED8F8' } },
            { value: 1, name: '轻微异常', itemStyle: { color: '#FACC15' } },
            { value: 1, name: '中等异常', itemStyle: { color: '#E98A3C' } },
            { value: 1, name: '严重异常', itemStyle: { color: '#A6343D' } }
          ]
        }
      ]
    }
  }

  const healthPercentage = Math.round((structureData.基本完好 / total) * 100);

  // 图表配置
  return {
    title: [
      {
        text: structureType,
        left: 'center',
        top: '35%',
        textStyle: {
          fontSize: 15,
          color: '#FACC15',
          fontWeight: 'bold'
        }
      },
      {
        // text: '健康度',
        left: 'center',
        top: '44%',
        textStyle: {
          fontSize: 12,
          color: '#FFFFFF',
          fontWeight: 'normal'
        }
      },
      {
        text: `${healthPercentage}%`,
        left: 'center',
        bottom: '5%',
        textStyle: {
          fontSize: 15,
          color: '#FFFFFF',
          fontWeight: 'heavy'
        }
      }
    ],
    series: [
      {
        name: '健康度分布',
        type: 'pie',
        radius: ['65%', '75%'],
        center: ['50%', '42%'],
        avoidLabelOverlap: false,
        padAngle: 5,
        itemStyle: {
          borderRadius: 10
        },
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false
        },
        startAngle: 150,
        emphasis: {
          scale: false
        },
        data: [
          {
            value: structureData.基本完好,
            name: '基本完好',
            itemStyle: { color: '#0ED8F8' }
          },
          {
            value: structureData.轻微异常,
            name: '轻微异常',
            itemStyle: { color: '#FACC15' }
          },
          {
            value: structureData.中等异常,
            name: '中等异常',
            itemStyle: { color: '#E98A3C' }
          },
          {
            value: structureData.严重异常,
            name: '严重异常',
            itemStyle: { color: '#A6343D' }
          }
        ]
      }
    ]
  };
}

// 计算各结构类型的图表选项
const bridgeOption = computed(() => createChartOption('桥梁'));
const tunnelOption = computed(() => createChartOption('隧道'));
const slopeOption = computed(() => createChartOption('边坡'));
</script>

<template>
  <div v-loading="isLoading" element-loading-text="正在加载数据..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.7)" class="w-full h-[310px]">
    <div v-if="error" class="w-full h-full flex justify-center items-center text-red-500">
      <span>数据获取失败</span>
    </div>
    <div v-else class="w-full h-full py-5 flex flex-col justify-around items-center">
      <div class="bg w-[360px] px-2 h-[95px] flex justify-around items-center">
        <div v-for="item in healths" :key="item.label" class="text-center">
          <div class="text-xs text-gray-300 mb-1">{{ item.label }}</div>
          <div @click="handleClick(item)" class="text-2xl font-bold cursor-pointer" :class="item.color">{{ item.value }}</div>
        </div>
      </div>
      <div class="w-[355px] h-[145px] flex justify-between items-center">
        <VChart :option="bridgeOption" autoresize />
        <VChart :option="tunnelOption" autoresize />
        <VChart :option="slopeOption" autoresize />
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg {
  background-image: url('/image/image copy.png');
  background-size: 100% 100%;
}
</style>