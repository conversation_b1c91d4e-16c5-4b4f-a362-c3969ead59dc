<template>
  <div class="bg-dark text-white h-full flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-dark-border backdrop-blur-md border-b border-dark-border sticky top-0 z-50">
      <div class="container mx-auto px-4 py-2">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
          <div class="flex items-center space-x-2 w-full md:w-auto">
            <i class="fa fa-tint text-primary text-2xl"></i>
            <h1 class="text-2xl md:text-3xl font-bold text-gradient">台风专题监控系统</h1>
          </div>
          <div class="flex items-center space-x-4 w-full md:w-auto justify-end">
            <div class="hidden md:flex items-center space-x-2">
              <i class="fa fa-clock-o text-secondary"></i>
              <span class="text-base md:text-lg">{{ currentTime }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <i class="fa fa-refresh text-secondary"></i>
              <span class="text-base md:text-lg">上次更新: {{ updateTime }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 关闭窗口按钮 -->
      <div class="absolute right-4 top-2 z-50">
        <button @click="closeWindow" class="text-white hover:text-red-400 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full w-10 h-10 flex items-center justify-center transition-all duration-200">
          <i class="fa fa-close text-lg"></i>
        </button>
      </div>
    </header>

    <!-- 主内容区 - 单列布局 -->
    <main class="container mx-auto px-4 py-6 flex-1">
      <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-full">
        <div v-if="!isTailwind" class="xl:col-span-1 space-y-4 overflow-y-auto h-full bg-dark-card border border-dark-border rounded-xl p-3 card-shadow flex justify-center items-center flex-col">
          <span>暂无台风</span>
        </div>
        <!-- 左侧：台风列表、台风详情、台风路径图 -->
        <div v-else class="xl:col-span-1 space-y-4 overflow-y-auto h-full">
          <!-- 台风列表 -->
          <TyphoonList :storm-list="stormList" :selected-storm-id="selectedStormId" @select-storm="selectStorm" />

          <!-- 台风详情 -->
          <TyphoonDetails :selected-storm-id="selectedStormId" :selected-storm-name="selectedStormName" :current-location="currentLocation" :typhoon-type="typhoonType" :current-pressure="currentPressure" :current-wind-speed="currentWindSpeed" :forecast12h="forecast12h" :forecast24h="forecast24h" :forecast48h="forecast48h" @refresh-storm-data="refreshStormData" />

          <!-- 台风路径图 -->
          <TyphoonPathChart :path-chart-option="pathChartOption" :chart-loading="chartLoading" />
        </div>

        <!-- 右侧：设备监控 -->
        <div v-loading="deviceLoading" class="xl:col-span-3 space-y-8 h-[975px] w-full flex">
          <DeviceMonitoring :device-items="deviceItems" />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import TyphoonList from './components/TyphoonList.vue'
import TyphoonDetails from './components/TyphoonDetails.vue'
import TyphoonPathChart from './components/TyphoonPathChart.vue'
import DeviceMonitoring from './components/DeviceMonitoring.vue'

// 导入配置和工具函数
import { TYPHOON_TYPE_MAPPING, WIND_DIRECTIONS } from './config/constants'
import { createTyphoonPathChart, createWindRoseOption } from './utils/chartUtils'
import { formatDateTime, getCurrentTime, showError } from './utils/common'
import { typhoonApi } from './services/typhoonApi'
import { TyphoonDataProcessor } from './utils/dataProcessor'
import { getDeviceLatestData } from '/@/api/dataview/structure'

// 响应式数据
const currentTime = ref('加载中...')
const updateTime = ref('加载中...')
const stormList = ref([])
const selectedStormId = ref(null)
const selectedStormName = ref('台风名称')
const chartLoading = ref(false)
const deviceLoading = ref(false)

// 台风详情数据
const currentLocation = ref('--')
const typhoonType = ref('--')
const currentPressure = ref('-- hPa')
const currentWindSpeed = ref('-- m/s')

// 预测数据
const forecast12h = reactive({ location: '--', wind: '--' })
const forecast24h = reactive({ location: '--', wind: '--' })
const forecast48h = reactive({ location: '--', wind: '--' })


// 设备卡片数据（每个设备一条）
const deviceItems = ref([])

// 台风路径图表配置
const pathChartOption = ref(null)


// 方法
const updateCurrentTime = () => {
  currentTime.value = getCurrentTime()
}

const isTailwind = ref(false)
// 获取台风列表
const fetchStormList = async () => {
  try {
    const data = await typhoonApi.getStormList()
    updateTime.value = formatDateTime(data.updateTime)

    // 过滤活跃台风
    const activeStorms = typhoonApi.filterActiveStorms(data.storm)
    stormList.value = activeStorms
    isTailwind.value = stormList.value.length > 0

    // 如果有活跃台风，默认选中第一个
    if (activeStorms.length > 0) {
      selectStorm(activeStorms[0].id)
    }
  } catch (error) {
    // 错误已在API服务中处理
    console.error('未获取到台风列表')
  }
}

// 获取台风预测数据
const fetchStormForecast = async (stormId) => {
  try {
    chartLoading.value = true
    const data = await typhoonApi.getStormForecast(stormId)

    updateTime.value = formatDateTime(data.updateTime)

    // 渲染台风详情
    renderStormDetails(data.forecast)

    // 渲染台风路径图
    renderTyphoonPathChart(data.forecast)
  } catch (error) {
    // 错误已在API服务中处理
    console.error('获取台风详情失败:', error)
    chartLoading.value = false
  } finally {
    chartLoading.value = false
  }
}

// 选择台风
const selectStorm = (stormId) => {
  if (selectedStormId.value === stormId) return

  selectedStormId.value = stormId

  // 获取台风详情
  const selectedStorm = stormList.value.find(storm => storm.id === stormId)
  if (selectedStorm) {
    selectedStormName.value = selectedStorm.name
    fetchStormForecast(stormId)
  } else {
    showError('未找到选中的台风信息')
  }
}

// 渲染台风详情
const renderStormDetails = (forecast) => {
  try {
    const processedData = TyphoonDataProcessor.processStormDetails(forecast)

    // 更新当前数据
    currentLocation.value = processedData.current.location
    currentPressure.value = processedData.current.pressure
    currentWindSpeed.value = processedData.current.windSpeed
    typhoonType.value = processedData.current.type

    // 更新预测数据
    forecast12h.location = processedData.forecast12h.location
    forecast12h.wind = processedData.forecast12h.wind
    forecast24h.location = processedData.forecast24h.location
    forecast24h.wind = processedData.forecast24h.wind
    forecast48h.location = processedData.forecast48h.location
    forecast48h.wind = processedData.forecast48h.wind
  } catch (error) {
    showError(`渲染台风详情时出错: ${error.message}`)
    console.error('渲染台风详情错误:', error)
  }
}

// 渲染台风路径图
const renderTyphoonPathChart = (forecast) => {
  try {
    pathChartOption.value = createTyphoonPathChart(forecast, TYPHOON_TYPE_MAPPING)
  } catch (error) {
    showError(`渲染台风路径图时出错: ${error.message}`)
    console.error('渲染台风路径图错误:', error)
  }
}

// 刷新台风数据
const refreshStormData = () => {
  if (selectedStormId.value) {
    fetchStormForecast(selectedStormId.value)
  } else {
    fetchStormList()
  }
}

// 风向角度转中文方位
const degToWindText = (deg) => {
  const dirs = ['北', '东北偏北', '东北', '东北偏东', '东', '东南偏东', '东南', '东南偏南', '南', '西南偏南', '西南', '西南偏西', '西', '西北偏西', '西北', '西北偏北']
  const d = ((Number(deg) % 360) + 360) % 360
  const idx = Math.round(d / 22.5) % 16
  return dirs[idx]
}

// 获取设备最新数据（来自 /hwm/structure/getDeviceLatestData）
const fetchDeviceLatest = async () => {
  try {
    deviceLoading.value = true
    const res = await getDeviceLatestData()
    const list = res?.data || res

    if (Array.isArray(list)) {
      // 将接口返回的每条记录映射为一个卡片
      deviceItems.value = list.map((item, idx) => {
        const name = `${item['测点位置'] || ''} - ${item['结构物名称'] || ''}`.trim() || (item['监测内容'] || '设备')

        switch (item.type) {
          case 1: { // 温度
            const value = typeof item['温度'] === 'number' ? item['温度'] : null
            if (value === null) return null
            return {
              key: `temperature-${idx}`,
              type: 'temperature',
              value,
              config: {
                id: '', icon: 'thermometer', color: 'warning', unit: '°C', name, rules: {
                  35: 'yellow',
                  37: 'orange',
                  40: 'red'
                }
              }
            }
          }
          case 2: { // 湿度
            const value = typeof item['湿度'] === 'number' ? item['湿度'] : null
            if (value === null) return null
            return {
              key: `humidity-${idx}`,
              type: 'humidity',
              value,
              config: {
                id: '', icon: 'wet', color: 'secondary', unit: '%', name
              }
            }
          }
          case 3: { // 降雨
            const v = typeof item['今日降雨量'] === 'number' ? item['今日降雨量'] : (typeof item['累计降雨量'] === 'number' ? item['累计降雨量'] : null)
            if (v === null) return null
            return {
              key: `rainfall-${idx}`,
              type: 'rainfall',
              value: v,
              config: { id: '', icon: 'rain', color: 'blue-400', unit: 'mm', name }
            }
          }
          case 4: { // 风速风向（新增）
            const speed = typeof item['风速'] === 'number' ? item['风速'] : '-'
            const direction = typeof item['风向'] === 'number' ? item['风向'] : '-'
            // if (speed === null || direction === null) return null
            const history = Array.isArray(item['历史数据']) ? item['历史数据'] : []
            return {
              key: `wind-${idx}`,
              type: 'wind',
              value: speed,
              trend: `风向: ${degToWindText(direction) ?? '-'} (${direction}°)`,
              chartOption: createWindRoseOption(history),
              config: { id: '', icon: 'wind', color: 'primary', unit: 'm/s', name, rules: [] }
            }
          }
          default:
            return null
        }
      }).filter(Boolean)
    }
  } catch (error) {
    console.error('获取设备最新数据失败:', error)
  } finally {
    deviceLoading.value = false
  }
}

// 定时器
let timeInterval = null
let deviceInterval = null

// 生命周期
onMounted(() => {
  // 更新当前时间
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)

  // 获取台风列表
  fetchStormList()

  // 获取设备实时数据
  fetchDeviceLatest()
  deviceInterval = setInterval(fetchDeviceLatest, 0.5 * 60 * 1000) // 每5分钟刷新一次
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  if (deviceInterval) {
    clearInterval(deviceInterval)
  }
})

// 定义 emit 事件
const emit = defineEmits(['close'])

const closeWindow = () => {
  // 通知父组件关闭弹窗
  emit('close')
}
</script>

<style scoped>
@import './styles/common.css';

scrollable-content {
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 和 Edge */
}

/* Chrome、Safari 等 WebKit 浏览器 */
.scrollable-content::-webkit-scrollbar {
  display: none;
}
</style>