<script setup>
// @ts-nocheck
import { computed } from 'vue'
import StatisticCard from './StatisticCard.vue'
import { useRoadFilterApi } from '/@/composables/useApi'
import { getMonitorResult } from '/@/api/dataview/base'

const { data } = useRoadFilterApi(getMonitorResult)

const idMap = {
  '桥梁': 'bridge',
  '隧道': 'tunnel',
  '边坡': 'slope',
  '下穿通道': 'underpass',
}

const monitorData = computed(() => {
  if (!data.value)
    return []

  const order = ['桥梁', '隧道', '边坡', '下穿通道']

  return order.map((label) => {
    const itemData = data.value[label]
    if (!itemData)
      return null
    return {
      id: idMap[label],
      label,
      rate: Math.round(itemData['完好率'] * 100),
      status: {
        normal: Number(itemData['基本完好'] || 0),
        warning: Number(itemData['轻微离线'] || 0),
        danger: Number(itemData['严重离线'] || 0),
      },
    }
  }).filter(Boolean)
})
</script>

<template>
  <div class="p-2 flex flex-col gap-1">
    <!-- 左侧布局 (桥梁, 隧道, 边坡) -->
    <div class="w-full">
      <StatisticCard v-if="monitorData[0]" :label="monitorData[0].label" :rate-value="monitorData[0].rate" :status-data="monitorData[0].status" />
    </div>

    <!-- 右侧布局 (下穿通道) -->

    <div class="flex w-full gap-2">
      <div class="flex flex-col w-[65%] gap-1">
        <StatisticCard v-for="item in monitorData.slice(1, -1)" :key="item.id" :label="item.label" :rate-value="item.rate" :status-data="item.status" />
      </div>
      <div class="w-[35%]">
        <StatisticCard v-for="item in monitorData.slice(-1)" :key="item.id" :label="item.label" :rate-value="item.rate" :status-data="item.status" />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 无需额外样式，所有样式已移至卡片组件中 */
</style>