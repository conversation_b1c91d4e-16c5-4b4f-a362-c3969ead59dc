<script setup>
// @ts-nocheck
import Card from '../Card/index.vue'
import Statistics from './Statistics/index.vue'
import Record from './Record/index.vue'
import Rate from './Rate/index.vue'
import Result from './Result/index.vue'
import Device from './Device/index.vue'
import Online from './Online/index.vue'
import { getDisposeRate, getOnlineRate } from '/@/api/dataview/other.ts'

const UltraLimit = defineAsyncComponent(() => import('./UltraLimit/index.vue'))
const isUltraLimit = ref(false)
const AlarmDate = ref(null)
/**
 * @param {Date} date
 */
const handleDateChange = (date) => {
}
const cancel = () => {
  isUltraLimit.value = false
  console.log(isUltraLimit.value);

}
// 在线率统计的多选框选项
const onlineCheckList1 = ref(['highway'])
const onlineCheckList2 = ref(['highway'])
</script>

<template>
  <div class="w-[440px] h-full absolute top-5 left-[40px] z-10">
    <Card title="预警统计">
      <Statistics />
    </Card>
    <Card title="预警记录">
      <template #header>
        <div class="gap-2 h-full flex items-center">
          <div class="w-[120px] h-full flex items-center">
            <el-date-picker size="small" @change="handleDateChange" v-model="AlarmDate" type="date" />
          </div>
          <div class="w-6 h-6 cursor-pointer" @click="isUltraLimit = true">
            <img src="/image/Frame 427318821.png" class="w-full h-full" alt="">
          </div>
        </div>
      </template>
      <Record :alarm-date="AlarmDate" v-model:isUltraLimit="isUltraLimit" />
    </Card>
    <Card title="处置率统计">
      <template #header>
        <el-checkbox-group v-model="onlineCheckList1" class="custom-checkbox-group">
          <el-checkbox-button label="highway">高速</el-checkbox-button>
          <el-checkbox-button label="national">国省道</el-checkbox-button>
        </el-checkbox-group>
      </template>
      <Rate :checklist="onlineCheckList1" />
    </Card>
  </div>
  <!-- right -->
  <div class="w-[440px] h-full absolute top-5 right-[40px]">
    <Card title="监测结果">
      <Result />
    </Card>
    <Card title="监测设备">
      <Device />
    </Card>
    <Card title="在线率统计">
      <template #header>
        <el-checkbox-group v-model="onlineCheckList2" class="custom-checkbox-group">
          <el-checkbox-button label="highway">高速</el-checkbox-button>
          <el-checkbox-button label="national">国省道</el-checkbox-button>
        </el-checkbox-group>
      </template>
      <Online :checklist="onlineCheckList2" />
    </Card>
  </div>
  <!-- 超限超载 -->
  <div v-if="isUltraLimit" class="z-[999] w-full h-full absolute top-0 left-0 bg-black">
    <UltraLimit :cancel="cancel" :alarm-date="AlarmDate" />
  </div>
</template>

<style scoped>
.custom-checkbox-group :deep(.el-checkbox-button__inner) {
  padding: 4px 12px;
  font-size: 14px;

}

.custom-checkbox-group :deep(.el-checkbox-button) {
  margin-left: 10px;
  --el-checkbox-button-checked-bg-color: #01B3E1
}
</style>