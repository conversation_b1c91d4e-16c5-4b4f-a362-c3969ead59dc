<script setup>
// @ts-nocheck
import { dataview } from '/@/stores/dataview'
import { storeToRefs } from 'pinia';

const store = dataview()
const { curPage, curStructureType } = storeToRefs(store)

const structureTypeMap = {
  bridge: 1,
  tunnel: 2,
  slope: 3,
  underpass: 4,
};

// 桥梁 隧道 边坡 下穿通道
const structures = [
  { label: '桥梁', value: 'bridge' },
  { label: '隧道', value: 'tunnel' },
  { label: '边坡', value: 'slope' },
  { label: '下穿通道', value: 'underpass' }
  // { label: '下穿通道', value: 'underpass',disable:['MonitorWarning'] }
]

// 记录鼠标悬停的结构
const hoveredStructure = ref(null)

// 判断结构是否禁用
const isStructureDisabled = (structureItem) => {
  return structureItem.disable && structureItem.disable.includes(curPage.value)
}

// 选择结构
const selectStructure = (value) => {
  const structureItem = structures.find(s => s.value === value)
  if (structureItem && isStructureDisabled(structureItem)) {
    return // 如果禁用，则不执行任何操作
  }

  const newType = store.curStructureType === value ? null : value;
  store.curStructureType = newType;

  // Remove any existing structure type filters
  store.removeFilterList('structureType');

  if (newType) {
    const numericType = structureTypeMap[newType];
    if (numericType) {
      store.pushFilterList({
        id: 'structureType',
        path: ['structureType'],
        value: numericType
      });
    }
  }
}
</script>

<template>
  <div class="bg bg-white absolute z-10 flex justify-center items-center ">
    <div v-for="i in structures" :key="i.value" @click="selectStructure(i.value)" @mouseover="hoveredStructure = i.value" @mouseleave="hoveredStructure = null" class="cursor-pointer" :class="{ 'opacity-50 cursor-not-allowed': isStructureDisabled(i) }">
      <div class="w-[120px] h-[127px] relative left-[20px] top-[10px]">
        <img :src="`/image/${i.value}${isStructureDisabled(i) ? '1' : (curStructureType === i.value || hoveredStructure === i.value ? '0' : '')
          }.png`" alt="" class="absolute z-0" style="color: bisque;">
        <div class="absolute z-10 left-2 top-2 text-[#00D0FF] text-[26px] font-normal leading-[30px] text-center">
          <!-- {{ i.label }} -->
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg {
  background: url('/image/base.png') no-repeat center center;
  background-size: 100% 100%;
  width: 1200px;
  height: 127px;
  left: 360px;
  bottom: 0%;
  /* border: 1px solid #00D0FF; */
}
</style>