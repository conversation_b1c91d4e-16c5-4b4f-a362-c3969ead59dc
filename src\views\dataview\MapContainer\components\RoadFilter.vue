<template>
  <div class="absolute z-20 top-5 left-[58%] -translate-x-1/2 h-10 ">
    <div class="road-filter-container">
      <div class="filter-options">
        <el-checkbox v-model="roadFilter.all" @change="handleAllChange">全选</el-checkbox>
        <el-checkbox v-model="roadFilter.highway" @change="handleSingleChange">高速</el-checkbox>
        <el-checkbox v-model="roadFilter.national" @change="handleSingleChange">国省道</el-checkbox>
        <el-checkbox v-model="roadFilter.rural" @change="handleSingleChange">农村公路</el-checkbox>
      </div>
      <div class="search-section">
        <el-popover placement="bottom" :width="300" trigger="click" v-model:visible="searchPopoverVisible">
          <template #reference>
            <el-button class="search-btn" circle>
              <el-icon>
                <Search />
              </el-icon>
            </el-button>
          </template>
          <div class="search-suggestions">
            <h4>搜索建议</h4>
            <el-input v-model="searchQuery" placeholder="请输入搜索关键词" clearable @input="handleSearch" />
            <div class="suggestion-list">
              <div v-for="(item, index) in filteredSuggestions" :key="index" class="suggestion-item" @click="selectSuggestion(item)">
                {{ item.name }}
              </div>
              <div v-if="filteredSuggestions.length === 0" class="no-suggestions">
                无匹配结果
              </div>
            </div>
          </div>
        </el-popover>
        <el-button class="refresh-btn" @click="refreshMap" circle>
          <el-icon>
            <Refresh />
          </el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup>
// @ts-nocheck
import { computed, ref, watch } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dataview } from '/@/stores/dataview'
import { storeToRefs } from 'pinia';
const store = dataview()
const { roadFilter, structureList } = storeToRefs(store)
// const { data } = useRoadFilterApi(getStructureData)

// watchEffect(() => {
//   if (data) {
//     structureList.value = data.value
//   }
// })

// Search functionality
const searchQuery = ref('')
const searchPopoverVisible = ref(false)

const suggestions = computed(() => {
  if (!structureList.value) {
    return []
  }
  // Use a map to handle potential duplicate names, though ideally codes are unique.
  // This returns an array of { name, structureUniqueCode }
  return structureList.value
    .filter(item => item.structureName && typeof item.structureUniqueCode !== 'undefined')
    .map(item => ({ name: item.structureName, id: item.structureUniqueCode }))
})

const filteredSuggestions = computed(() => {
  if (!searchQuery.value) return suggestions.value

  return suggestions.value.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// Check/uncheck all roadFilter
const handleAllChange = (val) => {
  roadFilter.value.highway = val
  roadFilter.value.national = val
  roadFilter.value.rural = val
}

// Handle individual filter changes
const handleSingleChange = () => {
  if (roadFilter.value.highway && roadFilter.value.national && roadFilter.value.rural) {
    roadFilter.value.all = true
  } else {
    roadFilter.value.all = false
  }
}

// Watch for individual roadFilter.value to update "all" state
watch(
  [() => roadFilter.value.highway, () => roadFilter.value.national, () => roadFilter.value.rural],
  () => {
    roadFilter.value.all = roadFilter.value.highway && roadFilter.value.national && roadFilter.value.rural
  }
)

// Handle search query
const handleSearch = () => {
  // Additional search logic if needed
}
const refreshMap = () => {
  // store.setFilterList([])
  window.location.reload()
}
// Select a suggestion
const selectSuggestion = (item) => {
  const selectedStructure = structureList.value.find(s => s.structureUniqueCode === item.id)
  if (selectedStructure) {
    store.selectStructure(selectedStructure)
    searchQuery.value = item.name
    searchPopoverVisible.value = false
    ElMessage.success(`已定位到: ${item.name}`)
  }
}
</script>
<style scoped>
.road-filter-container {
  background-color: #0a1446;
  /* Dark blue background */
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  box-sizing: border-box;
  border-radius: 4px;
  /* Optional: rounded corners */
}

.filter-options {
  display: flex;
  gap: 15px;
  /* Added gap for better spacing */
  margin-right: 15px;
}

.search-section {
  display: flex;
  gap: 10px;
}

.search-btn,
.refresh-btn {
  color: #00D0FF;
  background: transparent;
  border: 1px solid #00D0FF;
}

.search-btn:hover,
.refresh-btn:hover {
  background: #00D0FF1A;
  /* Slight background on hover */
  color: #33daff;
}

/* Styles for the custom themed popover */
:deep(.blue-theme-search-popover) {
  background-color: #0a1446 !important;
  /* Your dark blue background */
  border: 1px solid #00D0FF !important;
  /* Your theme's accent color */
  border-radius: 4px;
  padding: 0;
  /* Remove default el-popover padding if any, control with children */
}

:deep(.blue-theme-search-popover .el-popper__arrow::before) {
  background-color: #0a1446 !important;
  /* Arrow background */
  border-color: #00D0FF !important;
  /* Arrow border */
}

.search-suggestions {
  /* This class is inside your popover template slot */
  padding: 15px;
  /* Add padding here instead of popover root */
  color: white;
}

.search-suggestions h4 {
  color: white;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

/* Styling for el-input within the themed popover */
:deep(.blue-theme-search-popover .el-input .el-input__wrapper) {
  background-color: #1c2a5e;
  /* A slightly lighter dark blue for input field */
  box-shadow: 0 0 0 1px #00D0FF inset;
  /* Custom border to match theme */
  border-radius: 4px;
}

:deep(.blue-theme-search-popover .el-input .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #33ebff inset;
  /* Brighter border on focus */
}

:deep(.blue-theme-search-popover .el-input .el-input__inner) {
  color: white;
  /* Text color */
}

:deep(.blue-theme-search-popover .el-input .el-input__inner::placeholder) {
  color: #88a1d0;
  /* Placeholder text color */
}

:deep(.blue-theme-search-popover .el-input .el-input__icon) {
  color: #88a1d0;
  /* Clear icon color */
}

:deep(.blue-theme-search-popover .el-input .el-input__clear:hover svg) {
  color: #00D0FF !important;
  /* Clear icon color on hover */
}


.suggestion-list {
  margin-top: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-list::-webkit-scrollbar {
  width: 6px;
}

.suggestion-list::-webkit-scrollbar-thumb {
  background: #00D0FF;
  border-radius: 3px;
}

.suggestion-list::-webkit-scrollbar-track {
  background: #1c2a5e;
}


.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: white;
  /* Text color for suggestions */
  border-radius: 3px;
}
</style>