import { ref, UnwrapRef, watch, watchEffect, Ref } from 'vue';
import { storeToRefs } from 'pinia';
import { dataview } from '/@/stores/dataview'; // 你的 Pinia store
import type { AxiosResponse } from 'axios'; // 导入 AxiosResponse 类型
import { debounce } from '/@/utils/debounce';
import dayjs from 'dayjs';

/**
 * 一个 Composable 函数，用于获取并自动更新数据。
 * 当 dataview store 中的 roadFilter 变化时，它会自动重新获取数据。
 * @template TData API 返回的数据的期望类型，默认为 any。
 * @param apiFn 一个函数，用于调用 API 并返回一个 Promise<AxiosResponse<TData>>。
 */
export function useRoadFilterApi<TData = any>(apiFn: () => Promise<AxiosResponse<TData>>) {
	const dataviewStore = dataview();
	const { roadFilter } = storeToRefs(dataviewStore);

	const data = ref<TData | null>(null);
	const isLoading = ref(false);
	const error = ref<Error | null>(null);

	const fetchData = async () => {
		isLoading.value = true;
		error.value = null;
		try {
			const response: AxiosResponse<TData> = await apiFn();
			data.value = response.data as UnwrapRef<TData>;
		} catch (err: any) {
			console.error('Composable: Failed to fetch data:', err);
			error.value = err;
			data.value = null;
		} finally {
			isLoading.value = false;
		}
	};

	const debouncedFetchData = debounce(fetchData, 300);

	watch(
		roadFilter,
		() => {
			debouncedFetchData();
		},
		{
			deep: true,
			immediate: true,
		}
	);

	return { data, isLoading, error };
}

/**
 * A composable function that automatically fetches and updates data based on changes
 * to both a date and the roadFilter from the dataview store.
 * @template TData The expected type of the API response data, defaults to any.
 * @param apiFn A function that calls the API. It should accept params for the date range
 *              and data for the road filter.
 * @param dateRef A ref<Date> object that the API call depends on.
 */
export function useDateAndRoadFilterApi<TData = any>(
	apiFn: (params: { createTime: number[] }, data: any) => Promise<AxiosResponse<TData>>,
	dateRef: Ref<Date>
) {
	const dataviewStore = dataview();
	const { roadFilter } = storeToRefs(dataviewStore);

	const data = ref<TData | null>(null);
	const isLoading = ref(false);
	const error = ref<Error | null>(null);

	const fetchData = async () => {
		isLoading.value = true;
		error.value = null;
		try {
			let params: any = {};
			if (dateRef.value) {
				const startOfDay = dayjs(dateRef.value).startOf('day').valueOf();
				const endOfDay = dayjs(dateRef.value).endOf('day').valueOf();
				params.createTime = [startOfDay, endOfDay];
			}
			const response: AxiosResponse<TData> = await apiFn(params, roadFilter.value);
			data.value = response.data as UnwrapRef<TData>;
		} catch (err: any) {
			console.error('Composable: Failed to fetch data:', err);
			error.value = err;
			data.value = null;
		} finally {
			isLoading.value = false;
		}
	};

	const debouncedFetchData = debounce(fetchData, 300);

	watch(
		[roadFilter, dateRef],
		() => {
			debouncedFetchData();
		},
		{
			deep: true,
			immediate: true,
		}
	);

	return { data, isLoading, error };
}

/**
 * An advanced composable that fetches data based on a date, the global roadFilter,
 * and an additional object of search parameters.
 * @param apiFn The API function to call.
 * @param dateRef A reactive Date object.
 * @param searchParamsRef A reactive object containing additional search parameters.
 */
export function useAdvancedFilterApi<TData = any>(
	apiFn: (params: any, data: any) => Promise<AxiosResponse<TData>>,
	dateRef: Ref<Date>,
	searchParamsRef: Ref<Record<string, any>>
) {
	console.log('searchParamsRef', searchParamsRef);
	
	const dataviewStore = dataview();
	const { roadFilter } = storeToRefs(dataviewStore);

	const data = ref<TData | null>(null);
	const isLoading = ref(false);
	const error = ref<Error | null>(null);

	const fetchData = async () => {
		isLoading.value = true;
		error.value = null;
		try {
			// Filter out empty/null values from searchParamsRef
			const filteredSearchParams = Object.entries(searchParamsRef.value)
				.filter(([_, value]) => {
					// 对于数组类型（如 timeRange），检查是否为空数组
					if (Array.isArray(value)) {
						return value.length > 0;
					}
					return value !== '' && value !== null && value !== undefined;
				})
				.reduce((acc, [key, value]) => {
					acc[key] = value;
					return acc;
				}, {} as Record<string, any>);

			let params: { [key: string]: any } = { ...filteredSearchParams };

			// 处理时间范围：优先使用用户选择的 timeRange，否则使用 dateRef
			if (params.timeRange && Array.isArray(params.timeRange) && params.timeRange.length === 2) {
				// 用户选择了时间范围，转换为时间戳
				const startTime = dayjs(params.timeRange[0]).valueOf();
				const endTime = dayjs(params.timeRange[1]).valueOf();
				params.createTime = [startTime, endTime];
				delete params.timeRange; // 删除 timeRange，使用 createTime
			} else if (dateRef.value) {
				// 没有用户选择的时间范围，使用默认的 dateRef
				const startOfDay = dayjs(dateRef.value).startOf('day').valueOf();
				const endOfDay = dayjs(dateRef.value).endOf('day').valueOf();
				params.createTime = [startOfDay, endOfDay];
			}

			const response: AxiosResponse<TData> = await apiFn(params, roadFilter.value);
			data.value = response.data as UnwrapRef<TData>;
		} catch (err: any) {
			console.error('Composable: Failed to fetch data:', err);
			error.value = err;
			data.value = null;
		} finally {
			isLoading.value = false;
		}
	};

	const debouncedFetchData = debounce(fetchData, 300);

	watch(
		[roadFilter, dateRef, searchParamsRef],
		() => {
			debouncedFetchData();
		},
		{
			deep: true,
			immediate: true,
		}
	);

	return { data, isLoading, error };
}