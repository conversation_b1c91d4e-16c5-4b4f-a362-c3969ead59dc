<script setup>
// @ts-nocheck
import VChart from 'vue-echarts';
import * as echarts from 'echarts';
import { watch, unref, computed } from 'vue';
import { getBridgeAndTunnelTechnicalCondition } from '/@/api/dataview/base';
import { useRoadFilterApi } from '/@/composables/useApi';

const { data: technicalConditionData, isLoading, error } = useRoadFilterApi(getBridgeAndTunnelTechnicalCondition); // Specify TData as any[] or your actual data type


const config = {
  colors: {
    categories: ['#2DCBFE', '#3BFDEF', '#FACE34', '#FF9B13', '#D22340'],
    status: ['#3694FF', '#AFC7E8'],
  },
  pieLayout: {
    centerY: '50%',
    statusRing: { radius: ['20%', '24%'] },
    categoryRing: { radius: ['29%', '35%'], padAngle: 5 },
    borderRing: { radius: ['39%', '40%'], color: '#fff' },
    title: {
      fontSize: 16,
      fill: '#FFFFFF',
    },
  },
  chartAppearance: {
    legend: {
      top: '10%',
      left: 'center',
      itemGap: 15,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: { color: '#FFFFFF' },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: '#fff'
    },
  },
};

// Computed property to generate ECharts option dynamically
const dynamicOption = computed(() => {
  if (error.value) {
    return {
      title: {
        text: '数据获取失败',
        left: 'center',
        top: 'center',
        textStyle: { color: '#fff' }
      }
    };
  }
  // Ensure technicalConditionData.value is not null or undefined and is an array
  if (!technicalConditionData.value || !Array.isArray(technicalConditionData.value) || technicalConditionData.value.length === 0) {
    // Return a basic or loading state option if data is not ready
    return {
      // Optionally, show a loading animation or a message
      title: {
        text: '',
        left: 'center',
        top: 'center',
        textStyle: { color: '#fff' }
      }
    };
  }

  const currentChartDataSource = technicalConditionData.value;
  const seriesArray = [];
  const graphicElements = [];
  const numCharts = currentChartDataSource.length;

  currentChartDataSource.forEach((chartItem, index) => {
    if (!chartItem || typeof chartItem.name === 'undefined' || !Array.isArray(chartItem.statusData) || !Array.isArray(chartItem.categoryData)) {
      return; // Skip this item if it's not valid
    }
    const center = (100 / numCharts) * (index + 0.5)
    const centerX = `${center}%`;
    const chartCenter = [centerX, config.pieLayout.centerY];

    seriesArray.push({
      name: `${chartItem.name} 评估状态`,
      type: 'pie',
      radius: config.pieLayout.statusRing.radius,
      center: chartCenter,
      avoidLabelOverlap: false,
      label: { show: false },
      labelLine: { show: false },
      emphasis: { label: { show: false }, scale: true, scaleSize: 5 },
      data: chartItem.statusData.map((item, i) => ({
        value: item.value,
        name: item.name,
        itemStyle: { color: config.colors.status[i % config.colors.status.length] } // Added modulo for safety
      }))
    });

    seriesArray.push({
      name: `${chartItem.name} 分类`,
      type: 'pie',
      radius: config.pieLayout.categoryRing.radius,
      center: chartCenter,
      avoidLabelOverlap: false,
      label: { show: false },
      labelLine: { show: false },
      minAngle: 5,
      padAngle: config.pieLayout.categoryRing.padAngle,
      emphasis: { label: { show: false }, scale: true, scaleSize: 5 },
      data: chartItem.categoryData.map((item, i) => ({
        value: item.value,
        name: item.name,
        itemStyle: { color: config.colors.categories[i % config.colors.categories.length] } // Added modulo for safety
      }))
    });

    seriesArray.push({
      name: `${chartItem.name} Border`,
      type: 'pie',
      radius: config.pieLayout.borderRing.radius,
      center: chartCenter,
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      emphasis: { scale: false },
      data: [{
        value: 1,
        name: '',
        itemStyle: { color: config.pieLayout.borderRing.color }
      }]
    });

    let textLeft = chartCenter[0];
    let textTop = chartCenter[1];

    graphicElements.push({
      type: 'text',
      left: `${parseFloat(textLeft) - 3.5}%`, // Ensure correct calculation from string percentage
      top: `${parseFloat(textTop) - 2}%`,   // Ensure correct calculation from string percentage
      style: {
        text: chartItem.name,
        fill: config.pieLayout.title.fill,
        fontSize: config.pieLayout.title.fontSize,
        textAlign: 'center',
        textVerticalAlign: 'middle'
      }
    });
    // 计算评估率 - 假设statusData中包含评估状态数据
    // 这里假设第一个状态是"已评估"，其余为未评估
    const totalItems = chartItem.statusData.reduce((sum, item) => sum + item.value, 0);
    const evaluatedItems = chartItem.statusData[0]?.value || 0;
    const evaluationRate = totalItems > 0 ? Math.round((evaluatedItems / totalItems) * 100) : 0;

    // 添加评估率文本（从statusData计算得出）
    graphicElements.push({
      type: 'text',
      left: center - 10 + '%',  // 与饼图水平居中对齐
      top: `${parseFloat(config.pieLayout.centerY) + 25}%`,  // 在饼图下方
      style: {
        // text: `已评估:${chartItem.statusData[0]?.value}`,  // 显示计算得出的评估率
        text: `已评定:${evaluationRate}%`,  // 显示计算得出的评估率
        fill: config.pieLayout.rateText?.fill || '#fff',
        fontSize: config.pieLayout.rateText?.fontSize || 15,
        fontWeight: 'bold',
      }
    });
  });

  // Check if currentChartDataSource[0] and its statusData are valid before accessing them for legend
  const legendStatusDataNames = (currentChartDataSource[0] && Array.isArray(currentChartDataSource[0].statusData))
    ? currentChartDataSource[0].statusData.map((item) => item.name)
    : [];

  return {
    legend: {
      ...config.chartAppearance.legend,
      data: [
        ...config.colors.categories.map((_, i) => `${i + 1}类`),
        ...legendStatusDataNames
      ],
    },
    tooltip: config.chartAppearance.tooltip,
    series: seriesArray,
    graphic: graphicElements
  };
});

</script>

<template>
  <div v-loading="isLoading" element-loading-text="正在加载数据..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.7)" class="w-full h-[310px]">
    <!-- Bind VChart to the computed dynamicOption -->
    <VChart :option="dynamicOption" autoresize />
  </div>
</template>

<style scoped></style>