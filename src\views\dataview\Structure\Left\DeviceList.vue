<template>
  <div class="w-full h-full p-2 border border-[#0E3067] bg-[#031538] text-white font-sans flex flex-col">
    <header class="flex justify-between items-center mb-3 flex-shrink-0">
      <span class="border-l-2 border-blue-500 pl-2 text-base">设备列表</span>
      <div style="width: 150px">
        <el-input v-model="searchQuery" placeholder="搜索设备" clearable />
      </div>
    </header>
    <div class="overflow-y-auto no-scrollbar flex-grow">
      <div v-if="devices.length > 0" class="grid grid-cols-2 gap-1 items-start">
        <el-tooltip v-for="item in devices" :key="item.id" :disabled="!item.monitorContent && !item.pointPosition" placement="top" effect="dark" popper-class="dataview-custom-tooltip">
          <template #content>
            <div>
              <div v-if="item.monitorContent"><span class="tooltip-label">监测项:</span> {{ item.monitorContent }}</div>
              <div v-if="item.pointPosition"><span class="tooltip-label">测点位置:</span> {{ item.pointPosition }}</div>
            </div>
          </template>
          <div class="transition-colors" :class="[
            item.status === '0'
              ? 'bg-gray-700 border-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-[#092B5C] border border-[#0F3974] cursor-pointer hover:border-blue-400',
            { '!border-blue-400 bg-[#0E3872]': isSelected(item) && item.status !== '0' },
          ]" @click="toggleSelect(item)">
            <div class="p-3 flex items-center space-x-3 h-[65px]">
              <div class="w-2.5 h-2.5 rounded-full flex-shrink-0" :class="getAlarmColorClass(item.alarmLevel, item.status)"></div>
              <div class="flex flex-col flex-1 min-w-0">
                <template v-if="isChinese(item.deviceName)">
                  <!-- 中文设备名：先显示设备名，再显示监测项+序号 -->
                  <div v-if="item.deviceName" class="text-[14px] truncate">{{ item.deviceName }}</div>
                  <div v-if="item.monitorContent" class="text-[12px] text-gray-300 truncate">
                    {{ item.monitorContent }}{{ item.serialNumber ? ` (${item.serialNumber})` : '' }}
                  </div>
                </template>
                <template v-else>
                  <!-- 非中文设备名：先显示监测项+序号，再显示设备名 -->
                  <div v-if="item.monitorContent" class="text-[14px] truncate">
                    {{ item.monitorContent }}{{ item.serialNumber ? ` (${item.serialNumber})` : '' }}
                  </div>
                  <div v-if="item.deviceName" class="text-[12px] text-gray-300 truncate">{{ item.deviceName }}</div>
                </template>
              </div>
            </div>
          </div>
        </el-tooltip>
      </div>
      <div v-else class="flex items-center justify-center h-full text-gray-400">
        暂无设备
      </div>
    </div>
  </div>
</template>

<script setup>


const props = defineProps({
  devices: {
    type: Array,
    default: () => [],
  },
  maxSelectCount: {
    type: Number,
    default: 1,
  },
})

const selectedDevices = defineModel('selectedDevices', {
  type: Array,
  default: () => [],
})

const searchQuery = defineModel('searchQuery', {
  type: String,
  default: '',
})

const getAlarmColorClass = (alarmLevel, status) => {
  const color = +alarmLevel || (+status ? 4 : 0)

  switch (color) {
    case 1:
      return 'bg-yellow-400'
    case 2:
      return 'bg-orange-500'
    case 3:
      return 'bg-red-600'
    case 4:
      return 'bg-green-500'
    default:
      return 'bg-gray-400'
  }
}

// 判断文本是否包含中文字符
const isChinese = (text) => {
  if (!text) return false
  return /[\u4e00-\u9fa5]/.test(text)
}

const isSelected = (item) => {
  return selectedDevices.value.some((device) => device.id === item.id)
}

const toggleSelect = (item) => {
  if (item.status === '0') return

  let updatedSelection = [...selectedDevices.value]
  const maxSelect = props.maxSelectCount ?? 1

  if (isSelected(item)) {
    updatedSelection = updatedSelection.filter((device) => device.id !== item.id)
  } else {
    if (updatedSelection.length >= maxSelect) {
      updatedSelection.shift()
    }
    updatedSelection.push(item)
  }

  selectedDevices.value = updatedSelection
}
</script>

<style>
.dataview-custom-tooltip .tooltip-label {
  color: #30c7db;
  margin-right: 8px;
}

.dataview-custom-tooltip {
  background-color: #051e3a !important;
  color: white !important;
  padding: 8px 10px !important;
  border-radius: 4px !important;
  max-width: 300px !important;
  word-break: break-all !important;
  z-index: 1000 !important;
  pointer-events: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #1c63bc !important;
}

.dataview-custom-tooltip .el-popper__arrow::before {
  background: #051e3a !important;
  border-color: #1c63bc !important;
}
</style>

<style scoped>
/* Add any additional scoped styles if needed */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
</style>