<template>
  <div class="bg-dark-card border border-dark-border rounded-xl p-3 card-shadow flex justify-between flex-col">
    <div class="flex justify-between items-center mb-2">
      <h3 class="font-medium flex items-center text-base">
        <img class="w-6 h-6 mr-2" :src="`/image/icons/${config.icon}.svg`"></img>
        {{ config.name }}
      </h3>
      <span v-if="config.id" class="text-sm text-gray-400">{{ config.id }}</span>
    </div>

    <!-- 风向监测特殊布局 -->
    <div v-if="type === 'windDirection'" class="flex flex-col items-center">
      <div class="w-16 h-16 relative mb-2">
        <div class="absolute inset-0 flex items-center justify-center">
          <i class="fa fa-compass text-3xl text-gray-500"></i>
        </div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="w-1 h-8 bg-purple-500 rounded-t-full transform origin-bottom transition-transform duration-500" :style="{ transform: `rotate(${value.direction}deg)` }"></div>
        </div>
      </div>
      <div class="text-xl font-bold">{{ value.text }}</div>
      <div class="text-sm text-gray-400">{{ value.direction }}°</div>
    </div>

    <!-- 其他设备通用布局 -->
    <div v-else class="flex items-end justify-between">
      <div>
        <div class="text-3xl font-bold">
          <span :class="'text-' + getColor(config.rules, value) + '-500'">{{ value }}</span>{{ config.unit }}
        </div>
        <div class="text-sm text-gray-400 mt-1">
          <slot name="trend">
            <span v-if="trend">{{ trend }}</span>
          </slot>
        </div>
      </div>
      <div v-if="chartOption" :class="type === 'wind' ? 'w-32 h-32' : 'w-16 h-16'">
        <v-chart :option="chartOption" autoresize class="w-full h-full" />
      </div>
    </div>
  </div>
</template>

<script setup>
import VChart from 'vue-echarts'
import 'echarts'

// Props
const props = defineProps({
  type: {
    type: String,
    required: true
  },
  config: {
    type: Object,
    required: true
  },
  value: {
    type: [String, Number, Object],
    required: true
  },
  chartOption: {
    type: Object,
    default: null
  },
  trend: {
    type: String,
    default: ''
  }
})
function getColor(rules = [], value) {
  let color = 'white'
  Object.keys(rules).forEach(key => {
    if (value >= +key) color = rules[key]
  })
  return color
}
</script>

<style scoped>
@import '../styles/common.css';
</style>
