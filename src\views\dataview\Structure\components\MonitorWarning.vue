<script setup>
import { ref, computed } from 'vue'
import VChart from 'vue-echarts'
import 'echarts'
import { getWarningRecord } from '/@/api/dataview/other';
import { useAdvancedFilterApi } from '/@/composables/useApi';
import { useChart } from '/@/composables/useChart'
import { dataview } from '/@/stores/dataview';
const { structureCode } = storeToRefs(dataview())
import dayjs from 'dayjs'

// Props
const props = defineProps({
  deviceCode: {
    type: String,
    default: '',
  },
  structureType: {
    type: Number,
    default: 0,
  },
  structureId: {
    type: Number,
    default: null,
  },
  selectedDevices: {
    type: Array,
    default: () => [],
  },
})

// Chart related state - 使用composable
const dataTab = ref('实时数据')
const chartProps = { ...props, dataTab }
const {
  chartData,
  baseData,
  isLoading,
  hasErrorOrNoData,
  legendSelected,
  option,
  handleLegendSelectChanged,
  setupWatchers
} = useChart(chartProps)

// 设置监听器
setupWatchers()

// Warning records related state
const alarmDate = ref(new Date())

const searchParamsRef = computed(() => ({ structureUniqueCode: structureCode.value }));

// console.log(structureUniqueCode);
const { data: warningData } = useAdvancedFilterApi(getWarningRecord, {}, searchParamsRef);

// Warning records computed
const warningRecords = computed(() => {
  if (!warningData.value) return [];
  return warningData.value.map((item) => ({
    alarmId: item.alarmId,
    alarmLevel: item.alarmLevel,
    alarmContent: item.alarmContent,
    alarmStatus: item.alarmStatus === 'Unhandled' ? 'unhandled' : 'handled',
    alarmStartTime: dayjs(item.alarmStartTime, 'x').format('MM-DD'),
    handleTime: item.handleTime ? dayjs(item.handleTime, 'x').format('MM-DD') : null
  }));
});

const getLevelStyle = (level, isHandled) => {
  const baseClasses = 'text-white text-xs font-bold'
  if (level === 1) return `${baseClasses} ${isHandled ? 'bg-red-400' : 'bg-red-600'}`
  if (level === 2) return `${baseClasses} ${isHandled ? 'bg-orange-400' : 'bg-orange-600'}`
  if (level === 3) return `${baseClasses} ${isHandled ? 'bg-yellow-400' : 'bg-yellow-600'}`
  return `${baseClasses} ${isHandled ? 'bg-blue-400' : 'bg-blue-600'}`
}

const getStatusStyle = (status) => {
  return status === 'handled' ? 'text-green-400' : 'text-red-400'
}


</script>

<template>
  <div class="w-full h-full flex flex-col">
    <!-- 图表区域 -->
    <div class="w-full h-1/2 p-2 border border-[#0E3067] bg-[#031538] flex flex-col">
      <div class="flex-grow min-h-0" v-loading="isLoading">
        <v-chart v-if="chartData" :option="option" autoresize @legendselectchanged="handleLegendSelectChanged" />
        <div v-else class="flex justify-center items-center h-full">
          <p v-if="hasErrorOrNoData" class="text-gray-400">暂无数据</p>
          <p v-else class="text-gray-400">请先在上方选择一个监测点位</p>
        </div>
      </div>
    </div>
    <!-- 预警列表区域 -->
    <div class="w-full h-1/2 p-2 border border-[#0E3067] bg-[#031538] flex flex-col">
      <header class="flex justify-between items-center mb-3 flex-shrink-0">
        <div class="flex items-center gap-2">
          <span class="border-l-2 border-blue-500 pl-2 text-base text-white">预警记录</span>
        </div>
      </header>
      <div class="flex-grow text-[12px] text-white h-full">
        <table class="w-full warning-table h-full">
          <thead>
            <tr>
              <th class="py-1 px-1 w-[80px]">等级</th>
              <th class="py-1 px-1">预警内容</th>
              <th class="py-1 px-1 w-[70px]">是否处置</th>
              <th class="py-1 px-1 w-[70px]">预警时间</th>
              <th class="py-1 px-1 w-[70px]">处置时间</th>
            </tr>
          </thead>
          <tbody class="scrollable-tbody h-full" style="height: 90%;">
            <tr v-if="!warningRecords || warningRecords.length === 0">
              <td colspan="5" class="py-10 text-center">暂无数据</td>
            </tr>
            <template v-else>
              <tr v-for="record in warningRecords" :key="record.alarmId">
                <td class="py-1 px-1 text-center w-[80px]">
                  <div class="w-9 h-9 flex items-center justify-center rounded-md m-auto" :class="getLevelStyle(record.alarmLevel, record.alarmStatus === 'handled')">
                    {{ record.alarmLevel }}
                  </div>
                </td>
                <td class="py-1 px-1 text-center">{{ record.alarmContent }}</td>
                <td class="py-1 px-1 text-center w-[70px]">
                  <span :class="getStatusStyle(record.alarmStatus)">
                    {{ record.alarmStatus === 'handled' ? '已处置' : '未处置' }}
                  </span>
                </td>
                <td class="py-1 px-1 text-center w-[70px]">{{ record.alarmStartTime }}</td>
                <td class="py-1 px-1 text-center w-[70px]">{{ record.handleTime || '-' }}</td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dataview-tabs :deep(.el-radio-button__inner) {
  background-color: transparent;
  color: white;
  border: 1px solid #456591;
  border-left: 0;
}

.dataview-tabs :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-left: 1px solid #456591;
}

.dataview-tabs :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #258eff;
  border-color: #258eff;
  color: white;
  box-shadow: -1px 0 0 0 #258eff;
}

.dataview-tabs :deep(.el-radio-button__inner:hover) {
  color: #258eff;
}

.warning-table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  table-layout: fixed;
}

.warning-table th {
  background-color: #031133;
  color: white;
  font-weight: normal;
  border: 1px solid #021132;
  text-align: center;
}

.warning-table td {
  border: 1px solid #021132;
  background-color: transparent;
  color: white;
}

.warning-table tr:hover td {
  background-color: rgba(11, 43, 122, 0.2);
}

.warning-table thead,
.scrollable-tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.scrollable-tbody {
  display: block;
  height: 200px;
  overflow-y: auto;
  scrollbar-width: none;
}

.scrollable-tbody::-webkit-scrollbar {
  display: none;
}
</style>
