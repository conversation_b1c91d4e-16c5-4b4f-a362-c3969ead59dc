<template>
  <div class="w-full h-full bg-[#003366] p-2 text-white overflow-hidden flex flex-col">
    <h1 class="text-xl lg:text-2xl font-bold mb-4 lg:mb-6 text-center flex-shrink-0">预警记录</h1>
    <div v-if="isLoading" class="text-center py-10 flex-1 flex items-center justify-center">加载中...</div>
    <div v-else class="flex-1 overflow-y-auto min-h-0">
      <table class="w-full text-xs warning-table">
        <thead class="sticky top-0 bg-[#003366] z-10">
          <tr>
            <th class="py-2 px-1 w-[40px]">等级</th>
            <th class="py-2 px-1 min-w-[80px]">预警内容</th>
            <th class="py-2 px-1 w-[60px]">处置状态</th>
            <th class="py-2 px-1 w-[60px]">预警时间</th>
            <th class="py-2 px-1 w-[60px]">处置时间</th>
            <th class="py-2 px-1 w-[65px]">预警类型</th>
            <th class="py-2 px-1 min-w-[60px]">管养单位</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="!tableData || tableData.length === 0">
            <td colspan="7" class="py-10 text-center">暂无数据</td>
          </tr>
          <template v-else>
            <tr v-for="row in tableData" :key="row.alarmId" @click="handleRowClick(row)" class="cursor-pointer" :class="{ 'selected-row': selectedAlarm && row.alarmId === selectedAlarm.alarmId }">
              <td class="p-2 text-center">
                <span :class="getLevelClass(row.alarmLevel)">
                  {{ row.alarmLevel }}
                </span>
              </td>
              <td class="p-2 text-center break-words max-w-[120px]">{{ row.alarmContent }}</td>
              <td class="p-2 text-center">
                <span :class="getStatusClass(row.alarmStatus)">
                  {{ row.alarmStatus }}
                </span>
              </td>
              <td class="p-2 text-center">{{ row.alarmStartTime }}</td>
              <td class="p-2 text-center">{{ row.handleTime }}</td>
              <td class="p-2 text-center">{{ row.sourceType }}</td>
              <td class="p-2 text-center break-words">{{ row.managementAlias }}</td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
// @ts-nocheck
import { computed } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  records: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  selectedAlarm: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['record-select']);

const tableData = computed(() => {
  return props.records.map((record) => ({
    ...record,
    alarmStatus: record.alarmStatus === 'Unhandled' ? '未处置' : '已处置',
    alarmStartTime: dayjs(record.alarmStartTime, 'x').format('MM-DD'),
    handleTime: record.handleTime ? dayjs(record.handleTime, 'x').format('MM-DD') : '',
  }));
});

const handleRowClick = (row) => {
  emit('record-select', { alarmId: row.alarmId, sourceType: row.sourceType });
};

const getLevelClass = (level) => {
  if (level === 0) return 'bg-blue-500 text-white px-2 py-1 rounded';
  if (level === 2) return 'bg-green-500 text-white px-2 py-1 rounded';
  return '';
};

const getStatusClass = (status) => {
  if (status === '未处置') return 'text-red-500';
  if (status === '已处置') return 'text-green-400';
  return '';
};
</script>

<style scoped>
.warning-table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  table-layout: fixed;
  width: 100%;
}

.warning-table th {
  background-color: #003366;
  /* Matching the container bg */
  color: white;
  font-weight: normal;
  border: 1px solid #021132;
  /* A darker border */
  text-align: center;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.warning-table td {
  border: 1px solid #021132;
  background-color: transparent;
  color: white;
  font-weight: 600;
  word-wrap: break-word;
  overflow-wrap: break-word;
  vertical-align: top;
}

.warning-table tr:hover td {
  background-color: #005099;
}

.warning-table .selected-row td {
  background-color: #005099;
}

/* 确保表格在小屏幕上也能正常显示 */
@media (max-width: 1024px) {
  .warning-table {
    font-size: 10px;
  }

  .warning-table th,
  .warning-table td {
    padding: 4px 2px;
  }
}
</style>
