<template>
  <div class="relative w-[420px] h-[192px] flex flex-col items-start isolate" :style="props.style">
    <div class="grid grid-cols-2 gap-8 m-auto">
      <div v-for="(item) in items" :key="item.id" class="flex flex-row items-center gap-4 relative cursor-default" :class="item.customGridClass" @mouseenter="hoveredItemId = item.id" @mouseleave="hoveredItemId = null">
        <div class="flex flex-row items-center gap-4 relative">
          <img :src="item.imageSrc" :alt="item.title" class="w-[60px] h-[60px] relative flex-shrink-0" />
          <div class="flex flex-col justify-start items-start gap-1 flex-shrink-0 relative">
            <span class="">
              {{ item.title }} <span class="text-sm font-['Microsoft_YaHei'] leading-5 tracking-[0.04em] text-white">({{ item.unit }})</span>
            </span>
            <span class="">
              <span class="text-xl cursor-pointer" @click="handleClick(item)"> {{ item.currentCount }}</span>
              <span class="text-xl text-[#2bccff]"> / {{ item.totalCount }} </span>
            </span>
          </div>
        </div>

        <div v-if="hoveredItemId === item.id && item.details && item.details.length > 0" class="custom-tooltip">
          <span class="text-[17px] text-[#2bccff] font-bold">分布情况</span>
          <div v-for="detail in item.details" :key="detail.label" class="mb-[4px] flex justify-between">
            <span class="tooltip-label pr-6">{{ detail.label }}</span>
            <span class="tooltip-value line text-[16px]">{{ detail.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// @ts-nocheck
import { ref, computed, onMounted } from 'vue';
import { getAssetsOverview } from '/@/api/dataview/other';
import { dataview } from '/@/stores/dataview';
import { storeToRefs } from 'pinia';

const store = dataview();
const { roadFilter } = storeToRefs(store);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const props = defineProps(['style']);

const hoveredItemId = ref(null);

// To hold raw API data
const overviewData = ref(null);

// Static configuration for items
const itemConfig = {
  '桥梁': {
    id: 'bridge',
    imageSrc: '/image/Frame 427318810.png',
    unit: '座',
  },
  '隧道': {
    id: 'tunnel',
    imageSrc: '/image/Frame 427318809.png',
    unit: '座',
    customGridClass: 'mt-[4px]',
  },
  '边坡': {
    id: 'slope',
    imageSrc: '/image/Frame 427318811.png',
    unit: '处',
  },
  '下穿通道': {
    id: 'underpass',
    imageSrc: '/image/Frame 427318808.png',
    unit: '处',
  },
};

const roadTypeMapping = {
  highway: '高速',
  national: '国省道',
  rural: '农村公路',
};

const items = computed(() => {
  const data = overviewData.value;
  if (!data) return [];

  return Object.keys(itemConfig)
    .filter(title => data[title]) // Ensure data exists for the configured item
    .map(title => {
      const structureData = data[title];
      const config = itemConfig[title];

      let currentCount = 0;
      let totalCount = 0;

      for (const roadKey of Object.keys(roadTypeMapping)) {
        if (roadFilter.value[roadKey]) {
          const roadTypeName = roadTypeMapping[roadKey];
          const roadData = structureData[roadTypeName];
          if (roadData) {
            currentCount += roadData.connected === '-' ? 0 : Number(roadData.connected);
            totalCount += Number(roadData.total);
          }
        }
      }

      const details = Object.keys(structureData)
        .map(roadTypeName => {
          const roadData = structureData[roadTypeName];
          // Ensure that we only create details for road types that exist in the mapping.
          if (Object.values(roadTypeMapping).includes(roadTypeName)) {
            return {
              label: roadTypeName,
              value: `${roadData.connected}/${roadData.total}`,
            };
          }
          return null;
        })
        .filter((detail) => detail !== null);

      return {
        ...config,
        title,
        currentCount,
        totalCount,
        details,
      };
    });
});

const handleClick = (item) => {
  const structureTypeMap = {
    bridge: 1,
    tunnel: 2,
    slope: 3,
    underpass: 4,
  };
  store.setFilterList([{ id: 'structure', path: ['structureType'], value: structureTypeMap[item.id] }]);
};

onMounted(async () => {
  try {
    const res = await getAssetsOverview();
    overviewData.value = res.data;
  } catch (error) {
    console.error('Failed to get assets overview:', error);
  }
});
</script>

<style scoped>
.custom-tooltip {
  position: absolute;
  bottom: 100%;
  /* Position above the parent */
  left: 50%;
  transform: translateX(-50%);
  background: #0d1a3dee;
  /* 深蓝色背景 */
  color: white;
  border: 2px solid #1a4195;
  border-radius: 4px;
  padding: 8px 10px;
  font-size: 12px;
  z-index: 10;
  white-space: nowrap;
  /* Prevent multi-line tooltips for now */
  margin-bottom: 8px;
  /* Space between item and tooltip */
}

.custom-tooltip::after {
  /* Arrow */
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #0d1a3d transparent transparent transparent;
}

.tooltip-label {
  color: white;
  font-size: 16px;
}

.tooltip-value {
  color: #2bccff;
  /* 与UI中其他数值颜色一致 */
  font-weight: bold;
}
</style>
1.333