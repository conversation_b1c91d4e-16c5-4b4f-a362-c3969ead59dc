import request from '/@/utils/request';

// 分页查询结构物列表
export const queryStructures = (params: any) => {
  return request({
    url: '/hwm/strData/queryStructures',
    method: 'get',
    params,
  });
};

// 分页查询桥梁列表 - structureType=1
export const queryBridges = (params: any) => {
  return request({
    url: '/hwm/strData/queryStructures',
    method: 'get',
    params: { ...params, structureType: 1 },
  });
};

// 分页查询隧道列表 - structureType=2
export const queryTunnels = (params: any) => {
  return request({
    url: '/hwm/strData/queryStructures',
    method: 'get',
    params: { ...params, structureType: 2 },
  });
};

// 分页查询边坡列表 - structureType=3
export const querySlopes = (params: any) => {
  return request({
    url: '/hwm/strData/queryStructures',
    method: 'get',
    params: { ...params, structureType: 3 },
  });
};

// 分页查询下穿通道列表 - structureType=4
export const queryUnderpassChannels = (params: any) => {
  return request({
    url: '/hwm/strData/queryStructures',
    method: 'get',
    params: { ...params, structureType: 4 },
  });
};
// 新增或修改结构物信息
export const addAndUpdate = (data: any) => {
  return request({
    url: '/hwm/strData/addAndUpdate',
    method: 'post',
    data,
  });
};
// 查询单个结构物详细信息
export const querySingleStructureInfo = (structureUniqueCode: number) => {
  return request({
    url: '/hwm/strData/querySingleStructureInfo',
    method: 'get',
    params: { structureUniqueCode },
  });
};
// 分页查询设备测点 /deviceMonitor/getDeviceMonitorData
export const getDeviceMonitorData = (params: any) => {
  return request({
    url: '/hwm/deviceMonitor/getDeviceMonitorData',
    method: 'get',
    params,
  });
};
// 查询设备测点详情 /deviceMonitor/getDeviceMonitorDetail
export const getDeviceMonitorDetail = (params: any) => {
  return request({
    url: '/hwm/deviceMonitor/getDeviceMonitorDetail',
    method: 'get',
    params,
  });
};
export const getHikVideoList = (code: number) => {
  return request({
    url: '/hwm/api/video/getVideoList',
    method: 'get',
    params: { structureUniqueCode: code },
  });
};

export const getSingleStructureInfo = (code: number) => {
  return request({
    url: '/hwm/structure/getSingleStructureInfo',
    method: 'get',
    params: { structureCode: code },
  });
};

export const getHikVideoUrl = (deviceSerial: string, structureUniqueCode: number, type: number) => {
  return request({
    url: '/hwm/api/video/getVideoUrl',
    method: 'get',
    params: { deviceSerial, structureUniqueCode, type }
  })
}

export const getRealTimeData = (pointUniqueCode: string, structureType: number, isRealTime: number) => {
  return request({
    url: '/hwm/structure/getRealTimeData',
    method: 'get',
    params: { pointUniqueCode, structureType, isRealTime },
  });
};

// 获取结构物设备最新数据列表
export const getDeviceLatestData = (params?: any) => {
  return request({
    url: '/hwm/structure/getDeviceLatestData',
    method: 'get',
    params,
  });
};
