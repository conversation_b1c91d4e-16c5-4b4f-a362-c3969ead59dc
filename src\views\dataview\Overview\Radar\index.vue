<script setup>
// @ts-nocheck
import { ref, watch } from 'vue';
import VChart from 'vue-echarts';
import { getComprehensiveIndex } from '/@/api/dataview/base'
import { useRoadFilterApi } from '/@/composables/useApi';

const { data: apiData } = useRoadFilterApi(getComprehensiveIndex);

// Base ECharts configurations
const baseChartOptions = {
  backgroundColor: 'transparent',
  tooltip: { trigger: 'item' }, // concrete tooltip set per chart below
};

const baseRadarConfig = {
  triggerEvent: true,
  axisNameGap: 5,
  splitArea: { show: false },
  axisLine: { lineStyle: { color: 'rgba(28, 189, 179, 0.3)', width: 1 } },
  splitLine: { lineStyle: { width: 1, color: 'rgba(28, 189, 179, 0.3)' } },
};

// 逆时针显示顺序（用于 tooltip 内部顺序，不改变雷达轴顺序）
const preferredMetricOrderCCW = ['健康度', '完好率', '在线率', '处置率', '巡查率'];
const indicator = [{ name: '健康度', max: 100 },
{ name: '巡查率', max: 100 },
{ name: '处置率', max: 100 },
{ name: '在线率', max: 100 },
{ name: '完好率', max: 100 }];
// 统一风格的 Tooltip 配置（深色背景、青色描边；同一行显示 标签 与 数值）
const getTooltipOptions = (indicator) => {
  const names = (indicator || []).map((i) => (typeof i === 'string' ? i : i.name));
  const order = preferredMetricOrderCCW.filter((n) => names.includes(n));

  return {
    trigger: 'item',
    backgroundColor: 'rgba(6, 26, 38, 0.95)',
    borderColor: '#1CBDB3',
    borderWidth: 1,
    padding: [10, 10],
    textStyle: { color: '#E6FFFB', fontSize: 16 },
    extraCssText: 'box-shadow:0 0 12px rgba(28,189,179,0.35);border-radius:4px;',
    zIndex: 100,
    formatter: (params) => {
      const values = Array.isArray(params?.value)
        ? params.value
        : (params?.data && Array.isArray(params.data.value) ? params.data.value : []);
      const title = params?.name ? `<div style="margin-bottom:4px;color:#8FE3DD;">${params.name}</div>` : '';
      const rows = order.map((n) => {
        const idx = names.indexOf(n);
        const v = idx >= 0 && values[idx] != null ? values[idx] : '-';
        return `<div style="display:flex;justify-content:space-between;gap:10px;line-height:22px;">
                  <span style="color:#E6FFFB;">${n}</span>
                  <span style="color:#FFFFFF;">${v}</span>
                </div>`;
      }).join('');

      return `${title}${rows}`;
    },
  };
};

const baseSeriesConfig = {
  type: 'radar',
  symbol: 'circle',
  symbolSize: 1,
  itemStyle: { color: '#FFFFFF', borderColor: '#1CBDB3', borderWidth: 1 },
  areaStyle: { color: 'rgba(28, 189, 179, 0.5)' }, // Common default area style
};

const optionMain = ref({
  ...baseChartOptions,
  tooltip: getTooltipOptions(indicator),
  radar: {
    ...baseRadarConfig,
    center: ['50%', '55%'],
    radius: '65%',
    splitNumber: 5,
    axisName: {
      color: '#FFFFFF',
      fontSize: 14,
      lineHeight: 20,
      formatter: (name) => name,
    },
    indicator: indicator,
  },
  series: [
    {
      ...baseSeriesConfig,
      emphasis: {
        lineStyle: { width: 3 },
        areaStyle: { color: '#287B8B' },
      },
      lineStyle: { color: '#287B8B', width: 2 },
      data: [
        {
          value: [],
          name: '整体情况',
          // areaStyle is inherited from baseSeriesConfig
        },
      ],
    },
  ],
});

const smallChartsData = ref([
  {
    id: 'bridge',
    title: '桥梁',
    indicator: [],
    values: [],
  },
  {
    id: 'tunnel',
    title: '隧道',
    indicator: [],
    values: [],
  },
  {
    id: 'slope',
    title: '边坡',
    indicator: [],
    values: [],
  },
  {
    id: 'underpass',
    title: '下穿通道',
    indicator: [],
    values: [],
  },
]);

const getSmallChartOption = (specificData) => {
  const res = {
    ...baseChartOptions,
    tooltip: getTooltipOptions(specificData.indicator),
    radar: {
      ...baseRadarConfig,
      center: ['50%', '50%'],
      radius: '80%',
      splitNumber: 4,
      axisName: { show: false },
      indicator: specificData.indicator,
    },
    series: [
      {
        ...baseSeriesConfig,
        emphasis: {
          lineStyle: { width: 2 },
          areaStyle: { color: 'rgba(28, 189, 179, 0.7)' },
        },
        lineStyle: { color: '#1CBDB3', width: 1.5 },
        data: [
          {
            value: specificData.values,
            name: specificData.title,
            // areaStyle is inherited from baseSeriesConfig
          },
        ],
      },
    ],
  };
  return res
};

watch(
  apiData,
  newData => {
    if (!newData || Object.keys(newData).length === 0) return;

    const parseApiValue = (value) => {
      if (value === '-') return 100;
      const num = parseFloat(value);
      return isNaN(num) ? 0 : Math.round(num * 100);
    };

    const chartMetricToApiMetric = {
      健康度: '健康度',
      巡查率: '巡查率',
      处置率: '处置率',
      在线率: '设备在线率',
      完好率: '完好率',
    };

    // Update main chart
    const mainScores = optionMain.value.radar.indicator.map((item) => {
      const apiKey = chartMetricToApiMetric[item.name];
      const value = newData[apiKey] && newData[apiKey]['全部'] ? newData[apiKey]['全部'] : 0;
      return parseApiValue(value);
    });
    optionMain.value.series[0].data[0].value = mainScores;

    // Update small charts
    const structureMapping = {
      bridge: '桥梁',
      tunnel: '隧道',
      slope: '边坡',
      underpass: '下穿通道',
    };

    const preferredMetricOrder = ['健康度', '巡查率', '处置率', '在线率', '完好率'];

    smallChartsData.value.forEach(chart => {
      const structureName = structureMapping[chart.id];
      const newIndicators = [];
      const newValues = [];

      preferredMetricOrder.forEach(chartMetricName => {
        const apiMetricName = chartMetricToApiMetric[chartMetricName];
        if (newData[apiMetricName] && newData[apiMetricName][structureName] !== undefined) {
          newIndicators.push({ name: chartMetricName, max: 100 });
          const value = newData[apiMetricName][structureName];
          newValues.push(parseApiValue(value));
        }
      });

      chart.indicator = newIndicators;
      chart.values = newValues;
    });
  },
  { deep: true }
);
</script>

<template>
  <div class="w-full h-full  text-white flex gap-1 items-center">
    <div class="flex flex-col justify-center items-center flex-1 h-full">
      <div class="flex-grow w-full">
        <v-chart :option="optionMain" autoresize />
      </div>
      <div class="w-20 h-14 text-center bim">
        <span>整体情况</span>
      </div>
    </div>
    <div class="flex flex-col items-center flex-1">
      <div class="grid grid-cols-2 gap-y-2 w-full">
        <div v-for="chartData in smallChartsData" :key="chartData.id" class="flex flex-col items-center">
          <div v-if="chartData.indicator && chartData.indicator.length > 0" class="w-full h-[50px]">
            <v-chart :option="getSmallChartOption(chartData)" autoresize />
          </div>
          <div v-else class="w-full h-[50px] flex items-center justify-center text-gray-400">
            <span>无数据</span>
          </div>
          <div class="w-20 h-8 text-center bim">
            <span>{{ chartData.title }}</span>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>
<style>
.bim {
  background-image: url('/image/Frame 427318824.svg');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>