{"name": "maintenance", "version": "5.8.0", "description": "公路养护系统", "author": "versoon", "license": "不对外分发 versoon 版权所有，请购买商业版权", "scripts": {"dev": "vite --force", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --outDir ./docker/dist/", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "prettier": "prettier --write ."}, "dependencies": {"@liveqing/liveplayer-v3": "3.7.37", "@amap/amap-jsapi-loader": "^1.0.1", "@axolo/json-editor-vue": "^0.3.2", "@chenfengyuan/vue-qrcode": "^2.0.0", "@element-plus/icons-vue": "^2.0.10", "@form-create/element-ui": "3.2.16", "@microsoft/fetch-event-source": "^2.0.1", "@popperjs/core": "2.11.8", "@smallwei/avue": "^3.6.5", "@types/smallwei__avue": "^3.0.5", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "animate.css": "^4.1.1", "autofit.js": "^3.2.8", "autoprefixer": "^10.4.7", "axios": "^1.3.3", "codemirror": "5.65.5", "crypto-js": "^3.1.9-1", "disable-devtool": "^0.3.8", "driver.js": "^0.9.8", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "element-plus": "2.5.5", "ezuikit-js": "^8.1.10", "flv.js": "^1.6.2", "form-create-designer": "3.2.10-oem", "highlight.js": "^11.7.0", "js-base64": "^3.7.7", "js-cookie": "^3.0.1", "json-editor-vue3": "^1.1.1", "lodash": "^4.17.21", "marked": "^12.0.2", "markmap-common": "0.15.6", "markmap-lib": "0.15.8", "markmap-view": "0.15.8", "mitt": "^3.0.0", "mysql2": "^3.14.2", "nprogress": "^0.2.0", "pinia": "2.0.32", "postcss": "8.4.40", "qrcode": "1.5.1", "qs": "^6.11.0", "screenfull": "^6.0.2", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "tailwindcss": "3.4.17", "v-calendar": "3.1.2", "vue": "3.4.15", "vue-clipboard3": "^2.0.0", "vue-echarts": "6.6.1", "vue-i18n": "9.2.2", "vue-router": "^4.1.6", "vue3-tree-org": "^4.2.2", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@swc/core": "1.6.13", "@types/crypto-js": "^4.2.2", "@types/markdown-it": "^14.1.1", "@types/node": "^18.14.0", "@types/nprogress": "^0.2.0", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.47", "consola": "^2.15.3", "cross-env": "7.0.3", "daisyui": "4.11.1", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "pinia-plugin-persist": "^1.0.0", "prettier": "2.8.4", "sass": "1.58.3", "terser": "^5.31.1", "typescript": "^4.9.5", "unplugin-auto-import": "^0.13.0", "vite": "^4.3.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://www.versoon.com"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"], "repository": {"type": "git", "url": "https://gitee.com/versoon/maintenance"}}