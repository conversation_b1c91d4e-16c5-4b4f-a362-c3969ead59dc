<script setup>
// @ts-nocheck
// false,structureType,1,string,1桥，2隧，3坡，4下穿
// false,managementName,宁波绕城东段高速公路有限公司,string,管养单位名称
// true,sourceType,特殊事件,string,超限预警，特殊事件
// false,alarmLevel,0,string,0特殊事件，1一级，2二级，3三级
// false,alarmStatus,Handled,string,Handled已处理；Processing处理中；Unhandled未处理
// false,structureName,绕东,string,结构物名称，模糊查询
// false,createTime,"[""1750902325000,1750902325000""]",array,"时间戳,隔开"

import { computed } from 'vue';
import { getWarningRecord } from '/@/api/dataview/other';
import { useDateAndRoadFilterApi } from '/@/composables/useApi';
import { ref, defineProps, toRefs } from 'vue';
import dayjs from 'dayjs'
import { dataview } from '/@/stores/dataview';
const isUltraLimit = defineModel('isUltraLimit', {
  type: Boolean,
  default: false
})
const props = defineProps({
  alarmDate: {
    type: Date,
    default: undefined
  }
});

const { alarmDate } = toRefs(props);

const store = dataview();

const { data } = useDateAndRoadFilterApi(getWarningRecord, alarmDate);

const warningRecords = computed(() => {
  if (!data.value) return [];
  return data.value.map((item) => ({
    alarmId: item.alarmId,
    alarmLevel: item.alarmLevel,
    alarmContent: item.alarmContent,
    alarmStatus: item.alarmStatus === 'Unhandled' ? 'unhandled' : 'handled',
    alarmStartTime: dayjs(item.alarmStartTime, 'x').format('MM-DD'),
    handleTime: item.handleTime ? dayjs(item.handleTime, 'x').format('MM-DD') : null
  }));
});
setInterval(() => { }, 500);
// 根据等级获取样式
const getLevelStyle = (level, isHandled) => {
  level = +level
  if (level === 3 && !isHandled) return 'bg-[#2C1535]  border-2 border-[#D22340] text-white'
  if (level === 2 && !isHandled) return 'bg-[#303634]  border-2 border-[#E1C33A] text-white'
  if (level === 1 && !isHandled) return 'bg-[#114976]  border-2 border-[#2DCBFE] text-white'
  if (level === 0 && !isHandled) return 'bg-[#114976]  border-2 border-[#2DCBFE] text-white'
  return 'bg-[#08394C]  border-2 border-[#17A591] text-white'
}

// 根据处置状态获取样式
const getStatusStyle = (status) => {
  return status === 'unhandled' ? 'text-red-500' : 'text-green-500'
}
const open = () => {
  console.log(isUltraLimit);

  isUltraLimit.value = true
}
</script>

<template>
  <div class="h-full text-[12px] text-white">
    <table class="w-full warning-table">
      <thead>
        <tr>
          <th class="py-1 px-1 w-[80px]">等级</th>
          <th class="py-1 px-1">预警内容</th>
          <th class="py-1 px-1 w-[70px]">是否处置</th>
          <th class="py-1 px-1 w-[70px]">预警时间</th>
          <th class="py-1 px-1 w-[70px]">处置时间</th>
        </tr>
      </thead>
      <tbody class="scrollable-tbody">
        <tr v-if="!warningRecords || warningRecords.length === 0">
          <td colspan="5" class="py-10 text-center">暂无数据</td>
        </tr>
        <template v-else>
          <tr @click="open()" v-for="record in warningRecords" :key="record.alarmId">
            <td class="py-1 px-1 text-center w-[80px]">
              <div class="w-9 h-9 flex items-center justify-center rounded-md m-auto" :class="getLevelStyle(record.alarmLevel, record.alarmStatus === 'handled')">
                {{ record.alarmLevel }}
              </div>
            </td>
            <td class="py-1 px-1 text-center">{{ record.alarmContent }}</td>
            <td class="py-1 px-1 text-center w-[70px]">
              <span :class="getStatusStyle(record.alarmStatus)">
                {{ record.alarmStatus === 'handled' ? '已处置' : '未处置' }}
              </span>
            </td>
            <td class="py-1 px-1 text-center w-[70px]">{{ record.alarmStartTime }}</td>
            <td class="py-1 px-1 text-center w-[70px]">{{ record.handleTime || '-' }}</td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
.warning-table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  table-layout: fixed;
  /* For consistent column widths */
}

.warning-table th {
  background-color: #031133;
  color: white;
  font-weight: normal;
  border: 1px solid #021132;
  text-align: center;
}

.warning-table td {
  border: 1px solid #021132;
  background-color: transparent;
  color: white;
}

.warning-table tr:hover td {
  background-color: rgba(11, 43, 122, 0.2);
}

.warning-table thead,
.scrollable-tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.scrollable-tbody {
  display: block;
  height: 250px;
  /* Matching original el-table height */
  overflow-y: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
}
</style>