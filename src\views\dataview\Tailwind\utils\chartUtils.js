import { COLORS } from '../config/constants'

// 基础图表配置
const baseChartConfig = {
  grid: { top: 0, left: 0, right: 0, bottom: 0 },
  xAxis: {
    type: 'category',
    show: false,
    data: ['', '', '', '', '', '', '']
  },
  yAxis: {
    type: 'value',
    show: false
  }
}

// 创建线性图表配置
export const createLineChartOption = (data, color, min, max, smooth = true) => ({
  ...baseChartConfig,
  yAxis: { ...baseChartConfig.yAxis, min, max },
  series: [{
    type: 'line',
    data,
    lineStyle: { color, width: 2 },
    areaStyle: { color: `${color}1A` }, // 添加透明度
    symbol: 'none',
    smooth
  }]
})

// 创建柱状图表配置
export const createBarChartOption = (data, color, min, max) => ({
  ...baseChartConfig,
  yAxis: { ...baseChartConfig.yAxis, min, max },
  series: [{
    type: 'bar',
    data,
    itemStyle: { color, borderRadius: [4, 4, 0, 0] },
    barWidth: '60%'
  }]
})

// 设备图表配置工厂函数
export const createChartOption = (type, data) => {
  const chartConfigs = {
    temperature: () => createLineChartOption(
      [25.2, 25.8, 26.1, 26.3, 26.5, 26.4, 26.5],
      COLORS.warning,
      24,
      28
    ),
    humidity: () => createLineChartOption(
      [72, 74, 76, 75, 77, 78, 78],
      COLORS.secondary,
      65,
      85
    ),
    windSpeed: () => createLineChartOption(
      [15.2, 16.5, 17.8, 18.2, 18.5, 18.3, 18.2],
      COLORS.primary,
      12,
      22
    ),
    pressure: () => createLineChartOption(
      [1004.2, 1003.8, 1003.5, 1003.0, 1002.5, 1002.2, 1002.0],
      COLORS.purple,
      1000,
      1006
    ),
    rainfall: () => createBarChartOption(
      [0, 2.5, 5.3, 8.2, 12.5, 18.7, 25.8],
      COLORS.blue,
      0,
      30
    ),
    wave: () => createLineChartOption(
      [2.1, 2.8, 3.2, 3.5, 3.2, 2.9, 3.2],
      COLORS.cyan,
      0,
      5
    ),
    visibility: () => createLineChartOption(
      [6.2, 7.5, 8.1, 8.5, 9.2, 8.8, 8.5],
      COLORS.yellow,
      0,
      15
    )
  }

  return chartConfigs[type] ? chartConfigs[type]() : null
}

// 风玫瑰图
export const createWindRoseOption = (history = []) => {
  const dirs = [
    '北', '东北偏北', '东北', '东北偏东',
    '东', '东南偏东', '东南', '东南偏南',
    '南', '西南偏南', '西南', '西南偏西',
    '西', '西北偏西', '西北', '西北偏北'
  ]
  const bins = Array(dirs.length).fill(0)
  const counts = Array(dirs.length).fill(0)

  const toIndex = (deg) => {
    const d = ((Number(deg) % 360) + 360) % 360
    const idx = Math.round(d / 22.5) % 16
    return idx
  }

  history.forEach((h) => {
    const dir = h['风向'] ?? h.direction ?? h.dir
    const spd = Number(h['风速'] ?? h.speed ?? h.spd)
    if (isNaN(spd)) return
    const i = toIndex(Number(dir))
    bins[i] += spd
    counts[i] += 1
  })

  const data = bins.map((sum, i) => (counts[i] ? +(sum / counts[i]).toFixed(2) : 0))

  return {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: (p) => `${dirs[p.dataIndex]}: ${p.value} m/s`
    },
    angleAxis: {
      type: 'category',
      data: dirs,
      axisLine: { lineStyle: { color: 'rgba(200,200,200,0.5)' } },
      axisLabel: {
        color: '#fff', // 深色标签确保可见（根据背景调整）
        interval: 0,   // 强制显示所有标签（关键！）
        fontSize: 12,
        margin: 200,    // 增大标签与轴线的距离（避免被裁剪）
        // 防止标签重叠的辅助配置
        rotate: 0,
        align: 'center'
      }
    },
    radiusAxis: {
      axisLine: { show: true },
      // axisLabel: { show: true, color: '#333' },
      splitLine: { lineStyle: { color: 'rgba(200,200,200,0.1)' } }
    },
    // 极坐标核心配置（控制整体布局，替代grid的作用）
    polar: {
      // 缩小极坐标半径，给标签留出外围空间（关键！）
      radius: '75%', // 半径占容器的75%（默认可能过大导致标签溢出）
      // 确保极坐标居中（避免偏移导致一侧标签被裁）
      center: ['50%', '50%'],
      // 极坐标内边距（进一步调整内容与边缘距离）
      padding: [30, 30, 30, 30]
    },
    series: [{
      type: 'bar',
      coordinateSystem: 'polar',
      data,
      roundCap: true,
      itemStyle: { color: COLORS.primary },
      barWidth: 18
    }]
  }
}




// 台风路径图表配置
export const createTyphoonPathChart = (forecast, typhoonTypeMapping) => {
  if (!forecast || forecast.length === 0) {
    return null
  }

  // 分离历史路径和预测路径
  const historicalPoints = forecast.filter(point => point.isHistorical)
  const forecastPoints = forecast.filter(point => !point.isHistorical)
  console.log(forecast);

  // 提取所有经纬度数据
  const allLats = forecast.map(point => parseFloat(point.lat) || 0)
  const allLons = forecast.map(point => parseFloat(point.lon) || 0)

  // 计算经纬度范围
  const minLat = Math.min(...allLats) - 1
  const maxLat = Math.max(...allLats) + 1
  const minLon = Math.min(...allLons) - 1
  const maxLon = Math.max(...allLons) + 1

  return {
    backgroundColor: 'transparent',
    grid: {
      left: '15%',
      right: '15%',
      top: '10%',
      bottom: '10%'
    },
    xAxis: {
      type: 'value',
      name: '经度(°E)',
      nameTextStyle: { color: 'rgba(255, 255, 255, 0.7)' },
      min: minLon,
      max: maxLon,
      axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisTick: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisLabel: { color: 'rgba(255, 255, 255, 0.7)' },
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
    },
    yAxis: {
      type: 'value',
      name: '纬度(°N)',
      nameTextStyle: { color: 'rgba(255, 255, 255, 0.7)' },
      min: minLat,
      max: maxLat,
      axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisTick: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
      axisLabel: { color: 'rgba(255, 255, 255, 0.7)' },
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: COLORS.primary,
      textStyle: { color: '#fff' },
      formatter: (params) => {
        // 根据系列索引确定是历史路径还是预测路径
        const isHistorical = params.seriesIndex === 0
        const points = isHistorical ? historicalPoints : forecastPoints
        const point = points[params.dataIndex]

        if (!point) return ''

        const date = new Date(point.fxTime || '')
        const timeStr = date.toString() !== 'Invalid Date'
          ? `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:00`
          : '时间未知'

        const pathType = isHistorical ? '历史路径' : `预测路径${point.forecastSource ? `(${point.forecastSource})` : ''}`

        return [
          `${pathType}`,
          `${timeStr}`,
          `位置: ${point.lat || '--'}°N, ${point.lon || '--'}°E`,
          `气压: ${point.pressure || '--'} hPa`,
          `风速: ${point.windSpeed || '--'} m/s`,
          `等级: ${point.type || '--'}`
        ].join('<br/>')
      }
    },
    legend: {
      data: ['历史路径', '预测路径'],
      textStyle: { color: 'rgba(255, 255, 255, 0.7)' },
      top: '5%'
    },
    series: [
      // 历史路径
      {
        name: '历史路径',
        type: 'line',
        data: historicalPoints.map(point => [
          parseFloat(point.lon) || 0,
          parseFloat(point.lat) || 0
        ]),
        lineStyle: {
          color: COLORS.primary,
          width: 3
        },
        itemStyle: {
          color: COLORS.primary,
          borderColor: '#fff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 8,
        markPoint: historicalPoints.length > 0 ? {
          data: [{
            coord: [
              parseFloat(historicalPoints[historicalPoints.length - 1].lon) || 0,
              parseFloat(historicalPoints[historicalPoints.length - 1].lat) || 0
            ],
            symbol: 'pin',
            symbolSize: 50,
            itemStyle: { color: COLORS.danger },
            label: {
              show: true,
              position: 'top',
              formatter: '当前位置',
              color: '#fff',
              fontSize: 12
            }
          }]
        } : undefined
      },
      // 预测路径
      {
        name: '预测路径',
        type: 'line',
        data: forecastPoints.map(point => [
          parseFloat(point.lon) || 0,
          parseFloat(point.lat) || 0
        ]),
        lineStyle: {
          color: COLORS.warning,
          width: 2,
          type: 'dashed'
        },
        itemStyle: {
          color: COLORS.warning,
          borderColor: '#fff',
          borderWidth: 1
        },
        symbol: 'diamond',
        symbolSize: 6
      }
    ]
  }
}
