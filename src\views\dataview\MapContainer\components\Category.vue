<template>
  <div class="h-[88%] flex absolute top-10 left-[24.5%] z-20" :class="{ 'w-56': isShow, 'w-auto pointer-events-none': !isShow }">
    <Transition>
      <div v-show="isShow" class="w-48 demo h-full gb text-[#2BCCFF] p-2 text-sm">
        <ul class="space-y-1">
          <li v-for="(category, catIndex) in categories" :key="catIndex">
            <!-- Category clickable header -->
            <div @click="toggleCategory(catIndex)" class="flex items-center p-2 rounded cursor-pointer">
              <span class="inline-block w-5 text-center text-[#2BCCFF] shrink-0"> <!-- Icon container -->
                {{ category.open ? '▼' : '▶' }}
              </span>
              <span class=" text-[16px] underline underline-offset-8">{{ category.name }}</span>
            </div>

            <!-- Items direct under category -->
            <ul v-if="category.open && category.items" class="mt-1 space-y-0.5">
              <li v-for="(item, itemIndex) in category.items" :key="itemIndex" @click="selectItem(category.name, item.name, item.disabled)" class="flex items-center p-1.5 pl-7 rounded hover:bg-sky-800/50 cursor-pointer text-white hover:text-white" :class="{ 'bg-sky-700': selectedItem === item.name && !item.disabled, 'opacity-50 cursor-not-allowed': item.disabled }">
                <span>{{ item.name }}</span>
                <span class="ml-1 text-sky-500">({{ item.count }})</span>
              </li>
            </ul>

            <!-- Groups under category -->
            <ul v-if="category.open && category.groups" class="mt-1 space-y-0.5">
              <li v-for="(group, groupIndex) in category.groups" :key="groupIndex">
                <!-- Group clickable header -->
                <div @click="toggleGroup(catIndex, groupIndex)" class="flex items-center p-1.5 pl-3 rounded hover:bg-sky-800/60 cursor-pointer">
                  <span class="inline-block w-5 text-center text-sky-400 shrink-0">
                    {{ group.open ? '▼' : '▶' }}
                  </span>
                  <span class="font-medium">{{ group.name }}</span>
                </div>
                <!-- Items under group -->
                <ul v-if="group.open" class="mt-1 space-y-0.5">
                  <li v-for="(item, itemIndex) in group.items" :key="itemIndex" @click="selectItem(category.name, item.name, item.disabled)" class="flex items-center p-1.5 pl-10 rounded hover:bg-sky-800/50 cursor-pointer text-gray-300 hover:text-white" :class="{ 'bg-sky-700': selectedItem === item.name && !item.disabled, 'opacity-50 cursor-not-allowed': item.disabled }">
                    <span>{{ item.name }}</span>
                    <span class="ml-1 text-sky-500">({{ item.count }})</span>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </Transition>
    <div @click="isShow = !isShow" class="w-5 h-5 grid rounded-r-md place-items-center border-2 ml-2 border-[#1C63BC] cursor-pointer pointer-events-auto">
      <el-icon size="large" color="#1C63BC">
        <ArrowLeft v-show="isShow" />
        <ArrowRight v-show="!isShow" />
      </el-icon>
    </div>
  </div>
</template>
<script setup>
// @ts-nocheck
import { ref, watchEffect } from 'vue';
import { dataview } from '/@/stores/dataview'
import { storeToRefs } from 'pinia';

const store = dataview()
const { structureList } = storeToRefs(store);
const selectedItem = ref(null);

const selectItem = (categoryName, itemName, isDisabled) => {
  if (isDisabled) return;

  if (selectedItem.value === itemName) {
    selectedItem.value = null;
    store.selectedCategoryItem = null;
  } else {
    selectedItem.value = itemName;
    store.selectedCategoryItem = { categoryName, itemName };
  }
};

const isShow = ref(false)
const categories = ref([
  {
    name: '按监测场景查看',
    open: true,
    items: [
      { name: '桥梁标准化监测' },
      { name: '长大桥梁监测' },
      { name: '结构风险高桥梁' },
      { name: '汇流急流区桥梁' },
      { name: '饱和交通桥梁' },
      { name: '重载交通桥梁' },
      { name: '隧道结构监测' },
      { name: '隧道机电监测' },
      { name: '边坡专业监测' },
      { name: '边坡事件监测' },
      { name: '下穿通道监测' },
    ],
  },
  {
    name: '按管养单位查看',
    open: true,
    groups: [
      {
        name: '普通公路',
        open: true,
        items: [
          { name: '鄞州分中心' },
          { name: '慈溪分中心' },
          { name: '镇海分中心' },
          { name: '江北分中心' },
          { name: '象山分中心' },
          { name: '宁海分中心' },
          { name: '海曙分中心' },
          { name: '余姚分中心' },
          { name: '奉化分中心' },
          { name: '北仑分中心' },
        ],
      },
      {
        name: '高速公路',
        open: true,
        items: [
          // This list will be dynamically populated
        ],
      },
    ],
  },
]);

watchEffect(() => {
  if (!structureList.value || !Array.isArray(structureList.value)) {
    return;
  }

  const sceneCounter = {};
  const unitCounter = {};
  const allUnits = new Set();

  for (const structure of structureList.value) {
    if (structure.monitorScenarios) {
      sceneCounter[structure.monitorScenarios] = (sceneCounter[structure.monitorScenarios] || 0) + 1;
    }
    if (structure.managementAlias) {
      unitCounter[structure.managementAlias] = (unitCounter[structure.managementAlias] || 0) + 1;
      allUnits.add(structure.managementAlias);
    }
  }

  // Dynamically populate the expressway list
  const managementCategory = categories.value.find(c => c.name === '按管养单位查看');
  if (managementCategory?.groups) {
    const generalRoadGroup = managementCategory.groups.find(g => g.name === '普通公路');
    const expresswayGroup = managementCategory.groups.find(g => g.name === '高速公路');

    if (generalRoadGroup && expresswayGroup) {
      const generalRoadItems = new Set(generalRoadGroup.items.map(item => item.name));
      const expresswayItems = Array.from(allUnits)
        .filter(unit => !generalRoadItems.has(unit))
        .map(name => ({ name, count: 0, disabled: true }));
      expresswayGroup.items = expresswayItems;
    }
  }

  // Update counts and disabled states
  categories.value.forEach(category => {
    const counter = category.name === '按监测场景查看' ? sceneCounter : unitCounter;

    if (category.items) {
      category.items.forEach(item => {
        item.count = counter[item.name] || 0;
        item.disabled = item.count === 0;
      });
    }
    if (category.groups) {
      category.groups.forEach(group => {
        group.items.forEach(item => {
          item.count = counter[item.name] || 0;
          item.disabled = item.count === 0;
        });
      });
    }
  });
});

const toggleCategory = (categoryIndex) => {
  if (categories.value[categoryIndex]) {
    categories.value[categoryIndex].open = !categories.value[categoryIndex].open;
  }
};

const toggleGroup = (categoryIndex, groupIndex) => {
  const category = categories.value[categoryIndex];
  if (category?.groups?.[groupIndex]) {
    category.groups[groupIndex].open = !category.groups[groupIndex].open;
  }
};
</script>
<style scoped>
.gb {
  /* padding: 4px; */
  background: #031037;
  box-shadow: inset 0px 0px 10px 2px #4d8ae6;
}

.demo::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

.demo {
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
  overflow-x: hidden;
  overflow-y: auto;
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>