<template>
  <div class="flex">
    <div class="grid grid-cols-2 xl:grid-cols-3 gap-3">
      <!-- 每个设备一张卡片 -->
      <DeviceCard v-for="(item, idx) in deviceItems" :key="item.key || idx" :type="item.type" :config="item.config" :value="item.value" :chart-option="item.chartOption || null" :trend="item.trend || ''" />
    </div>
  </div>
</template>

<script setup>
import DeviceCard from './DeviceCard.vue'

// Props
const props = defineProps({
  // 按设备渲染的条目
  deviceItems: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
@import '../styles/common.css';
</style>
