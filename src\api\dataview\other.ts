import request from '/@/utils/request';
// /device/getDisposeRate
export const getDisposeRate = (data: any) => {
  return request({
    url: '/hwm/device/getDisposeRate',
    method: 'post'
    , data
  })
}
// /device/getOnlineRate
export const getOnlineRate = (data: any) => {
  return request({
    url: '/hwm/device/getOnlineRate',
    method: 'post'
    , data
  })
}

// /structure/getAssetsOverview
export const getAssetsOverview = () => {
  return request({
    url: '/hwm/structure/getAssetsOverview',
    method: 'post'
  })
}

// /alarm/getWarningRecord
export const getWarningRecord = (params: any, data: any) => {
  return request({
    url: '/hwm/alarm/getWarningRecord',
    method: 'post',
    params,
    data,
  })
}

// /hwm/alarm/getAlarmInfo
export const getAlarmInfo = (params: { alarmId: string; alarmType: number }) => {
  return request({
    url: '/hwm/alarm/getAlarmInfo',
    method: 'get',
    params,
  });
};

export const getStructureMaintainUnit = () => {
  return request({
    url: '/hwm/structure/getStructureMaintainUnit',
    method: 'get',
  });
};

// 台风相关接口
// /typhoon/getTyphoonList
export const getTyphoonList = () => {
  return request({
    url: '/hwm/typhoon/getTyphoonList',
    method: 'get',
  });
};

// /typhoon/getTyphoonDetail
export const getTyphoonDetail = (tfid: string) => {
  return request({
    url: '/hwm/typhoon/getTyphoonDetail',
    method: 'get',
    params: { tfid },
  });
};