<template>
  <AvTable :option="option" :searchForm="searchForm" :listApi="getSpecialEvent" />
</template>

<script setup>
import AvTable from '/@/components/AvTable/index.vue'
import { getSpecialEvent } from '/@/api/alarm/specialEvent'

// 搜索表单配置
const searchForm = {
  structureType: '',
  managementName: '',
  eventType: '',
  status: '', // 修改为与column配置中的prop匹配
  structureName: ''
}
const column = [
  {
    label: '事件状态',
    prop: 'status',
    search: true,
    type: 'select',
    dicData: [
      { label: '已处理', value: 'Handled' },
      { label: '处理中', value: 'Processing' },
      { label: '未处理', value: 'Unhandled' }
    ],
    formatter: (row, column, cellValue) => {
      const statusMap = {
        'Handled': '已处理',
        'Processing': '处理中',
        'Unhandled': '未处理'
      }
      return statusMap[cellValue] || cellValue
    }
  },
  {
    label: '事件发生时间',
    prop: 'eventOccurTime',
    formatter: (row, column, cellValue) => {
      if (cellValue) {
        return new Date(parseInt(cellValue)).toLocaleString()
      }
      return ''
    }
  },
  {
    label: '处理人',
    prop: 'handleUser'
  },
  {
    label: '处理人电话',
    prop: 'handleUserTel'
  },
  {
    label: '处理时间',
    prop: 'handleTime',
    formatter: (row, column, cellValue) => {
      if (cellValue) {
        return new Date(parseInt(cellValue)).toLocaleString()
      }
      return ''
    }
  },
  {
    label: '事件类型',
    prop: 'eventType',
    search: true,
    type: 'select',
    dicData: [
      { label: '台风', value: 'Typhoon' },
      { label: '地震', value: 'Earthquake' },
      { label: '涡振', value: 'VortexVibration' },
      { label: '船撞', value: 'ShipHit' },
      { label: '冰冻', value: 'Frost' },
      { label: '大件运输', value: 'Transportation' },
      { label: '火灾', value: 'Fire' },
      { label: '危化品泄露', value: 'Leakage' },
      { label: '其它', value: 'Other' }
    ]
  },
  {
    label: '管养单位',
    prop: 'managementName',
    search: true
  },
  {
    label: '结构物名称',
    prop: 'structureName',
    search: true
  },
  // {
  //   label: '结构物类型',
  //   prop: 'structureType',
  //   search: true,
  //   type: 'select',
  //   dicData: [
  //     { label: '桥梁', value: '1' },
  //     { label: '隧道', value: '2' },
  //     { label: '边坡', value: '3' },
  //     { label: '下穿通道', value: '4' }
  //   ],
  //   formatter: (row, column, cellValue) => {
  //     const typeMap = {
  //       '1': '桥梁',
  //       '2': '隧道',
  //       '3': '边坡',
  //       '4': '下穿通道'
  //     }
  //     return typeMap[cellValue] || cellValue
  //   }
  // }
].map(i => ({ ...i, labelWidth: 120, searchLabelWidth: 100 }))

// 表格配置
const option = {
  border: true,
  stripe: true,
  menu: false, // 没有操作项
  addBtn: false,
  delBtn: false,
  editBtn: false,
  viewBtn: false,
  refreshBtn: true,
  index: true,
  columnBtn: false,
  header: false,
  align: 'center',
  searchMenuPosition: 'right',
  searchMenuSpan: 18,
  column,
}


</script>